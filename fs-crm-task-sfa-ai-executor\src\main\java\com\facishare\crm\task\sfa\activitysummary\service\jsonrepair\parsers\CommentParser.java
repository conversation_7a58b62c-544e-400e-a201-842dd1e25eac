package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.parsers;


import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.Constants;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.ContextValues;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonContext;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonParser;

import java.util.ArrayList;
import java.util.List;

public class CommentParser implements JsonElementParser {
    
    @Override
    public Object parse(JsonParser parser) {
        Character ch = parser.getCharAt();
        if (ch == null) {
            return Constants.EMPTY_STRING;
        }
        
        char c = ch;
        List<Character> terminationCharacters = new ArrayList<>();
        terminationCharacters.add('\n');
        terminationCharacters.add('\r');
        
        // Add context-specific termination characters
        JsonContext context = parser.getContext();
        if (containsContextValue(context, ContextValues.ARRAY)) {
            terminationCharacters.add(']');
        }
        if (containsContextValue(context, ContextValues.OBJECT_VALUE)) {
            terminationCharacters.add('}');
        }
        if (containsContextValue(context, ContextValues.OBJECT_KEY)) {
            terminationCharacters.add(':');
        }
        
        // Line comment starting with #
        if (c == Constants.COMMENT_HASH) {
            StringBuilder comment = new StringBuilder();
            while (ch != null && !terminationCharacters.contains(ch)) {
                comment.append(ch);
                parser.setIndex(parser.getIndex() + 1);
                ch = parser.getCharAt();
            }
            parser.log("Found line comment: " + comment + ", ignoring");
        }
        // Comments starting with '/'
        else if (c == Constants.COMMENT_SLASH) {
            Character nextChar = parser.getCharAt(1);
            if (nextChar != null && nextChar == '/') {
                // Handle line comment starting with //
                StringBuilder comment = new StringBuilder("//");
                parser.setIndex(parser.getIndex() + 2); // Skip both slashes
                ch = parser.getCharAt();
                while (ch != null && !terminationCharacters.contains(ch)) {
                    comment.append(ch);
                    parser.setIndex(parser.getIndex() + 1);
                    ch = parser.getCharAt();
                }
                parser.log("Found line comment: " + comment + ", ignoring");
            } else if (nextChar != null && nextChar == '*') {
                // Handle block comment starting with /*
                StringBuilder comment = new StringBuilder("/*");
                parser.setIndex(parser.getIndex() + 2); // Skip '/*'
                while (true) {
                    ch = parser.getCharAt();
                    if (ch == null) {
                        parser.log("Reached end-of-string while parsing block comment; unclosed block comment.");
                        break;
                    }
                    comment.append(ch);
                    parser.setIndex(parser.getIndex() + 1);
                    if (comment.toString().endsWith("*/")) {
                        break;
                    }
                }
                parser.log("Found block comment: " + comment + ", ignoring");
            } else {
                // Skip standalone '/' characters that are not part of a comment
                parser.setIndex(parser.getIndex() + 1);
            }
        }
        
        if (parser.getContext().isEmpty()) {
            // Need to continue parsing - this would require calling back to main parser
            // For now, return empty string
            return Constants.EMPTY_STRING;
        } else {
            return Constants.EMPTY_STRING;
        }
    }
    
    private boolean containsContextValue(JsonContext context, ContextValues value) {
        // Simple check - in a real implementation, you might want to check the context stack
        return context.getCurrent() == value;
    }
}