# JSON Repair Service

这是一个用于修复损坏或格式错误的JSON数据的Java服务包。

## 功能特性

### 🔧 核心修复功能

1. **基础修复**
   - 移除控制字符
   - 修复未引用的键名 (`{name: "value"}` → `{"name": "value"}`)
   - 修复单引号 (`{'name': 'value'}` → `{"name": "value"}`)
   - 移除尾随逗号 (`{"name": "value",}` → `{"name": "value"}`)
   - 移除JavaScript注释

2. **高级修复**
   - 添加缺失的逗号
   - 修复不匹配的括号
   - 处理截断的JSON
   - 修复值类型问题
   - JSON结构重构

3. **激进修复**
   - 提取有效的JSON片段
   - 重构损坏的JSON结构
   - 自动补全不完整的JSON

## 类结构

### JsonRepairService
基础的JSON修复服务，提供简单易用的修复功能。

```java
@Autowired
private JsonRepairService jsonRepairService;

// 修复JSON字符串
String repairedJson = jsonRepairService.repairJson(malformedJson);

// 安全解析JSON
JsonNode jsonNode = jsonRepairService.safeParseJson(malformedJson);

// 验证JSON有效性
boolean isValid = jsonRepairService.isValidJson(jsonString);
```

### JsonRepairUtil
提供静态方法的工具类，适合简单的修复需求。

```java
// 快速修复
String repaired = JsonRepairUtil.quickRepair(malformedJson);

// 深度修复
String repaired = JsonRepairUtil.deepRepair(malformedJson);

// 安全解析
JsonNode node = JsonRepairUtil.safeParseJson(malformedJson);

// 格式化JSON
String formatted = JsonRepairUtil.formatJson(malformedJson);
```

### AdvancedJsonRepairService
高级修复服务，支持多种修复策略和详细的配置选项。

```java
@Autowired
private AdvancedJsonRepairService advancedService;

// 使用不同的修复策略
RepairResult result = advancedService.repairJson(malformedJson, RepairStrategy.DEEP);

// 使用自定义配置
JsonRepairConfig config = JsonRepairConfig.STRICT;
RepairResult result = advancedService.repairJson(malformedJson, RepairStrategy.QUICK, config);

// 查看修复过程
if (result.isSuccess()) {
    System.out.println("修复成功: " + result.getRepairedJson());
    System.out.println("执行的操作: " + result.getRepairActions());
} else {
    System.out.println("修复失败: " + result.getError().getMessage());
}
```

### JsonRepairConfig
修复配置类，允许自定义修复行为。

```java
// 使用预定义配置
JsonRepairConfig config = JsonRepairConfig.DEFAULT;  // 默认配置
JsonRepairConfig config = JsonRepairConfig.STRICT;   // 严格模式
JsonRepairConfig config = JsonRepairConfig.LENIENT;  // 宽松模式

// 自定义配置
JsonRepairConfig customConfig = new JsonRepairConfig()
    .setFixUnquotedKeys(true)
    .setRemoveTrailingCommas(true)
    .setStrictMode(false)
    .setMaxLength(100000);
```

## 修复策略

### RepairStrategy.QUICK
- 执行基础的、安全的修复操作
- 速度最快，但修复能力有限
- 适合已知问题较少的JSON

### RepairStrategy.DEEP  
- 执行更全面的修复操作
- 包括括号匹配、截断处理等
- 推荐的默认策略

### RepairStrategy.AGGRESSIVE
- 尝试所有可能的修复方法
- 包括结构重构和片段提取
- 适合严重损坏的JSON

## 使用示例

### 基本使用

```java
@Service
public class DataProcessingService {
    
    @Autowired
    private JsonRepairService jsonRepairService;
    
    public void processApiResponse(String response) {
        // 修复可能损坏的API响应
        String cleanJson = jsonRepairService.repairJson(response);
        
        // 安全解析
        JsonNode data = jsonRepairService.safeParseJson(cleanJson);
        
        if (data != null) {
            // 处理数据...
        }
    }
}
```

### 高级使用

```java
@Service
public class AdvancedDataProcessor {
    
    @Autowired
    private AdvancedJsonRepairService repairService;
    
    public ProcessResult processComplexData(String jsonData) {
        // 使用严格模式进行修复
        JsonRepairConfig config = JsonRepairConfig.STRICT
            .setMaxLength(50000)
            .setLogRepairAttempts(true);
            
        RepairResult result = repairService.repairJson(
            jsonData, 
            RepairStrategy.DEEP, 
            config
        );
        
        if (result.isSuccess()) {
            logger.info("JSON修复成功，执行了 {} 个修复操作", 
                       result.getRepairActions().size());
            return ProcessResult.success(result.getRepairedJson());
        } else {
            logger.error("JSON修复失败: {}", result.getError().getMessage());
            return ProcessResult.failure(result.getError());
        }
    }
}
```

## 常见修复示例

### 1. 未引用的键名
```javascript
// 输入
{name: "John", age: 30}

// 输出  
{"name": "John", "age": 30}
```

### 2. 单引号问题
```javascript
// 输入
{'name': 'John', 'city': 'New York'}

// 输出
{"name": "John", "city": "New York"}
```

### 3. 尾随逗号
```javascript
// 输入
{"name": "John", "age": 30,}

// 输出
{"name": "John", "age": 30}
```

### 4. 缺失逗号
```javascript
// 输入
{"name": "John" "age": 30}

// 输出
{"name": "John", "age": 30}
```

### 5. 截断的JSON
```javascript
// 输入
{"name": "John", "details": {"city": "New

// 输出
{"name": "John", "details": {"city": "New"}}
```

## 配置选项

| 选项 | 默认值 | 说明 |
|------|--------|------|
| fixUnquotedKeys | true | 修复未引用的键名 |
| fixSingleQuotes | true | 修复单引号 |
| removeTrailingCommas | true | 移除尾随逗号 |
| addMissingCommas | true | 添加缺失的逗号 |
| removeComments | true | 移除JavaScript注释 |
| fixBrackets | true | 修复括号匹配 |
| handleTruncation | true | 处理截断问题 |
| maxLength | 1000000 | 最大JSON长度 |
| strictMode | false | 严格模式 |
| returnEmptyOnFailure | true | 失败时返回空对象 |

## 性能建议

1. **选择合适的策略**: 如果JSON问题较少，使用QUICK策略
2. **设置长度限制**: 对于大型JSON，设置合理的maxLength
3. **使用缓存**: 对于重复的JSON模式，考虑缓存修复结果
4. **监控修复日志**: 开启logRepairAttempts了解修复情况

## 注意事项

1. **数据安全**: 修复过程可能会改变原始数据的含义
2. **性能考量**: 激进修复策略可能比较耗时
3. **错误处理**: 始终检查修复结果的success状态
4. **日志记录**: 建议记录修复失败的原始JSON用于分析

## 异常处理

```java
try {
    RepairResult result = repairService.repairJson(malformedJson, RepairStrategy.DEEP);
    
    if (!result.isSuccess()) {
        // 处理修复失败
        logger.warn("JSON修复失败: {}", result.getError().getMessage());
        
        if (result.getError() instanceof JsonRepairException) {
            JsonRepairException jre = (JsonRepairException) result.getError();
            logger.debug("原始JSON: {}", jre.getOriginalJson());
            logger.debug("修复尝试: {}", jre.getRepairAttempt());
        }
    }
} catch (Exception e) {
    logger.error("JSON修复过程中发生异常", e);
}
```