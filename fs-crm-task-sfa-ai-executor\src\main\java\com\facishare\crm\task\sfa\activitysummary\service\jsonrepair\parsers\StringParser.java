package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.parsers;


import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.Constants;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.ContextValues;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonParser;

public class StringParser implements JsonElementParser {
    
    @Override
    public Object parse(JsonParser parser) {
        boolean missingQuotes = false;
        boolean doubledQuotes = false;
        char leftDelimiter = Constants.DEFAULT_STRING_DELIMITER;
        char rightDelimiter = Constants.DEFAULT_STRING_DELIMITER;

        Character ch = parser.getCharAt();
        if (ch == null) {
            return Constants.EMPTY_STRING;
        }
        
        char c = ch;
        
        if (c == Constants.COMMENT_HASH || c == Constants.COMMENT_SLASH) {
            return new CommentParser().parse(parser);
        }

        // Skip non-alphanumeric and non-delimiter characters
        while (ch != null && !Constants.STRING_DELIMITERS.contains(c) && !Character.isLetterOrDigit(c)) {
            parser.setIndex(parser.getIndex() + 1);
            ch = parser.getCharAt();
            if (ch == null) break;
            c = ch;
        }

        if (ch == null) {
            return Constants.EMPTY_STRING;
        }

        // Determine the correct delimiter
        if (c == '\'') {
            leftDelimiter = rightDelimiter = '\'';
        } else if (c == '"') {
            leftDelimiter = rightDelimiter = '"';
        } else if (c == '"') {
            leftDelimiter = '"';
            rightDelimiter = '"';
        } else if (Character.isLetterOrDigit(c)) {
            // This could be a boolean/null, not a string
            if ((c == 't' || c == 'T' || c == 'f' || c == 'F' || c == 'n' || c == 'N') 
                && parser.getContext().getCurrent() != ContextValues.OBJECT_KEY) {
                Object value = new BooleanOrNullParser().parse(parser);
                if (value != null && !value.equals(Constants.EMPTY_STRING)) {
                    return value;
                }
            }
            parser.log("While parsing a string, we found a literal instead of a quote");
            missingQuotes = true;
        }

        if (!missingQuotes) {
            parser.setIndex(parser.getIndex() + 1);
        }

        // Handle doubled quotes case
        Character nextChar = parser.getCharAt();
        if (nextChar != null && Constants.STRING_DELIMITERS.contains(nextChar) && nextChar == leftDelimiter) {
            // Check for empty key/value
            Character afterNext = parser.getCharAt(1);
            if ((parser.getContext().getCurrent() == ContextValues.OBJECT_KEY && afterNext != null && afterNext == ':') ||
                (parser.getContext().getCurrent() == ContextValues.OBJECT_VALUE && afterNext != null && (afterNext == ',' || afterNext == '}'))) {
                parser.setIndex(parser.getIndex() + 1);
                return Constants.EMPTY_STRING;
            }
            
            // Handle other doubled quote cases
            int skipIndex = parser.skipToCharacter(rightDelimiter, 1);
            Character foundChar = parser.getCharAt(skipIndex);
            if (foundChar != null && parser.getCharAt(skipIndex + 1) != null && parser.getCharAt(skipIndex + 1) == rightDelimiter) {
                parser.log("While parsing a string, we found a valid starting doubled quote");
                doubledQuotes = true;
                parser.setIndex(parser.getIndex() + 1);
            } else {
                int whitespaceSkip = parser.skipWhitespacesAt(1, false);
                Character afterWhitespace = parser.getCharAt(whitespaceSkip);
                if (afterWhitespace != null && (Constants.STRING_DELIMITERS.contains(afterWhitespace) || 
                    afterWhitespace == '{' || afterWhitespace == '[')) {
                    parser.log("While parsing a string, we found a doubled quote but also another quote afterwards, ignoring it");
                    parser.setIndex(parser.getIndex() + 1);
                    return Constants.EMPTY_STRING;
                } else if (afterWhitespace == null || (afterWhitespace != ',' && afterWhitespace != ']' && afterWhitespace != '}')) {
                    parser.log("While parsing a string, we found a doubled quote but it was a mistake, removing one quote");
                    parser.setIndex(parser.getIndex() + 1);
                }
            }
        }

        StringBuilder stringBuilder = new StringBuilder();
        ch = parser.getCharAt();
        
        while (ch != null && ch != rightDelimiter) {
            c = ch;
            
            // Handle missing quotes in object key context
            if (missingQuotes && parser.getContext().getCurrent() == ContextValues.OBJECT_KEY && 
                (c == ':' || Character.isWhitespace(c))) {
                parser.log("While parsing a string missing the left delimiter in object key context, we found a :, stopping here");
                break;
            }
            
            // Handle missing quotes in object value context
            if (!parser.isStreamStable() && parser.getContext().getCurrent() == ContextValues.OBJECT_VALUE &&
                (c == ',' || c == '}') && (stringBuilder.length() == 0 || stringBuilder.charAt(stringBuilder.length() - 1) != rightDelimiter)) {
                parser.log("While parsing a string missing the right delimiter in object value context, we found a terminator, stopping here");
                break;
            }

            // Handle missing quotes in array context
            if (missingQuotes && parser.getContext().getCurrent() == ContextValues.ARRAY && 
                (c == ',' || c == ']')) {
                parser.log("While parsing a string missing the left delimiter in array context, we found a terminator, stopping here");
                break;
            }

            // Handle escape sequences
            if (c == '\\') {
                Character nextEscapeChar = parser.getCharAt(1);
                if (nextEscapeChar != null) {
                    stringBuilder.append(c);
                    parser.setIndex(parser.getIndex() + 1);
                    stringBuilder.append(nextEscapeChar);
                    parser.setIndex(parser.getIndex() + 1);
                } else {
                    stringBuilder.append(c);
                    parser.setIndex(parser.getIndex() + 1);
                }
            } else {
                stringBuilder.append(c);
                parser.setIndex(parser.getIndex() + 1);
            }
            
            ch = parser.getCharAt();
        }

        // Skip the closing delimiter if we found it
        if (ch != null && ch == rightDelimiter) {
            parser.setIndex(parser.getIndex() + 1);
            if (doubledQuotes) {
                // Skip the second closing delimiter for doubled quotes
                Character finalChar = parser.getCharAt();
                if (finalChar != null && finalChar == rightDelimiter) {
                    parser.setIndex(parser.getIndex() + 1);
                }
            }
        }

        return stringBuilder.toString();
    }
}