package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair;

import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.Rule;
import org.junit.rules.TemporaryFolder;

import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * Comprehensive test cases for JsonRepair class
 *
 * Test coverage:
 * 1. Basic JSON repair functionality
 * 2. Different parameter combinations
 * 3. Edge cases and exception handling
 * 4. File reading functionality
 * 5. RepairResult class functionality
 */
public class JsonRepairTest {

    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();

    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testRepairJson_ValidJson() {
        // Test valid JSON string
        String validJson = "{\"name\":\"test\",\"value\":123}";
        String result = JsonRepair.repairJson(validJson);

        assertNotNull(result);
        try {
            objectMapper.readValue(result, Object.class);
        } catch (Exception e) {
            fail("Should be valid JSON: " + e.getMessage());
        }
    }

    @Test
    public void testRepairJson_InvalidJson() {
        // Test JSON string that needs repair
        String invalidJson = "{name:test,value:123}"; // Missing quotes
        String result = JsonRepair.repairJson(invalidJson);

        assertNotNull(result);
        // Result should be valid JSON
        try {
            objectMapper.readValue(result, Object.class);
        } catch (Exception e) {
            fail("Repaired JSON should be valid: " + e.getMessage());
        }
    }

    @Test
    public void testRepairJson_EmptyAndNull() {
        // Test null input
        String result1 = JsonRepair.repairJson(null);
        assertEquals("", result1);

        // Test empty string
        String result2 = JsonRepair.repairJson("");
        assertEquals("", result2);

        // Test whitespace string
        String result3 = JsonRepair.repairJson("   ");
        assertNotNull(result3);
    }

    @Test
    public void testRepairJson_ReturnObjects() {
        String jsonStr = "{\"name\":\"test\",\"value\":123}";

        // returnObjects = true, should return parsed object
        Object result = JsonRepair.repairJson(jsonStr, true, false, false);
        assertNotNull(result);
        assertTrue(result instanceof Map || result instanceof List ||
                  result instanceof String || result instanceof Number ||
                  result instanceof Boolean);

        // returnObjects = false, should return JSON string
        Object result2 = JsonRepair.repairJson(jsonStr, false, false, false);
        assertNotNull(result2);
        assertTrue(result2 instanceof String);
    }

    @Test
    public void testRepairJson_SkipJsonLoads() {
        String validJson = "{\"name\":\"test\"}";

        // skipJsonLoads = false, will try standard JSON parsing first
        Object result1 = JsonRepair.repairJson(validJson, false, false, false);
        assertNotNull(result1);

        // skipJsonLoads = true, directly use repair parser
        Object result2 = JsonRepair.repairJson(validJson, false, true, false);
        assertNotNull(result2);
    }

    @Test
    public void testRepairJson_Logging() {
        String jsonStr = "{name:test}"; // JSON that needs repair

        // logging = true, should return RepairResult object
        Object result = JsonRepair.repairJson(jsonStr, false, false, true);
        assertNotNull(result);
        assertTrue(result instanceof JsonRepair.RepairResult);

        JsonRepair.RepairResult repairResult = (JsonRepair.RepairResult) result;
        assertNotNull(repairResult.getResult());
        assertNotNull(repairResult.getLogs());
    }

    @Test
    public void testRepairJson_StreamStable() {
        String jsonStr = "{\"name\":\"test\"}";

        // Test streamStable = true
        Object result1 = JsonRepair.repairJson(jsonStr, false, false, false, true);
        assertNotNull(result1);

        // Test streamStable = false
        Object result2 = JsonRepair.repairJson(jsonStr, false, false, false, false);
        assertNotNull(result2);
    }

    @Test
    public void testLoads() {
        String jsonStr = "{\"name\":\"test\",\"value\":123}";
        Object result = JsonRepair.loads(jsonStr);

        assertNotNull(result);
        // loads method should return parsed object, not string
        assertFalse(result instanceof String);
    }

    @Test
    public void testLoad() throws IOException {
        String jsonStr = "{\"name\":\"test\",\"value\":123}";
        StringReader reader = new StringReader(jsonStr);

        Object result = JsonRepair.load(reader);
        assertNotNull(result);
    }

    @Test
    public void testLoad_IOException() throws IOException {
        // Test normal case
        StringReader reader = new StringReader("{}");
        Object result = JsonRepair.load(reader);
        assertNotNull(result);
    }

    @Test
    public void testFromFile() throws IOException {
        // Create temporary JSON file
        File jsonFile = tempFolder.newFile("test.json");
        String jsonContent = "{\"name\":\"test\",\"value\":123}";
        Files.write(jsonFile.toPath(), jsonContent.getBytes());

        Object result = JsonRepair.fromFile(jsonFile.getAbsolutePath());
        assertNotNull(result);
    }

    @Test(expected = IOException.class)
    public void testFromFile_FileNotFound() throws IOException {
        String nonExistentFile = "non_existent_file.json";
        JsonRepair.fromFile(nonExistentFile);
    }

    @Test
    public void testRepairJson_ComplexScenarios() {
        // Test key-value pairs missing quotes
        String json1 = "{name: 'test', value: 123}";
        String result1 = JsonRepair.repairJson(json1);
        assertNotNull(result1);
        try {
            objectMapper.readValue(result1, Object.class);
        } catch (Exception e) {
            fail("Repaired JSON should be valid: " + e.getMessage());
        }

        // Test array repair
        String json2 = "[1, 2, 3, ]"; // Trailing comma
        String result2 = JsonRepair.repairJson(json2);
        assertNotNull(result2);
        try {
            objectMapper.readValue(result2, Object.class);
        } catch (Exception e) {
            fail("Repaired JSON should be valid: " + e.getMessage());
        }

        // Test nested objects
        String json3 = "{user: {name: test, age: 25}}";
        String result3 = JsonRepair.repairJson(json3);
        assertNotNull(result3);
        try {
            objectMapper.readValue(result3, Object.class);
        } catch (Exception e) {
            fail("Repaired JSON should be valid: " + e.getMessage());
        }
    }

    @Test
    public void testRepairJson_SpecialCharacters() {
        // Test strings with special characters
        String json1 = "{\"message\": \"Hello\\nWorld\"}";
        String result1 = JsonRepair.repairJson(json1);
        assertNotNull(result1);
        try {
            objectMapper.readValue(result1, Object.class);
        } catch (Exception e) {
            fail("JSON with special characters should be valid: " + e.getMessage());
        }

        // Test Unicode characters
        String json2 = "{\"emoji\": \"😀\"}";
        String result2 = JsonRepair.repairJson(json2);
        assertNotNull(result2);
        try {
            objectMapper.readValue(result2, Object.class);
        } catch (Exception e) {
            fail("JSON with Unicode should be valid: " + e.getMessage());
        }
    }

    @Test
    public void testRepairJson_NumberTypes() {
        // Test integers
        String json1 = "{\"count\": 42}";
        String result1 = JsonRepair.repairJson(json1);
        assertNotNull(result1);

        // Test floating point numbers
        String json2 = "{\"price\": 19.99}";
        String result2 = JsonRepair.repairJson(json2);
        assertNotNull(result2);

        // Test scientific notation
        String json3 = "{\"value\": 1.23e-4}";
        String result3 = JsonRepair.repairJson(json3);
        assertNotNull(result3);
    }

    @Test
    public void testRepairJson_BooleanAndNull() {
        // Test boolean values
        String json1 = "{\"active\": true, \"disabled\": false}";
        String result1 = JsonRepair.repairJson(json1);
        assertNotNull(result1);

        // Test null values
        String json2 = "{\"data\": null}";
        String result2 = JsonRepair.repairJson(json2);
        assertNotNull(result2);
    }

    @Test
    public void testRepairJson_SerializationException() {
        // Create a scenario that would cause serialization exception
        // Here we simulate objectMapper.writeValueAsString throwing exception

        String jsonStr = "{\"test\": \"value\"}";

        // Should handle normally
        String result = JsonRepair.repairJson(jsonStr);
        assertNotNull(result);
    }

    @Test
    public void testRepairResult() {
        String jsonStr = "{name: test}"; // JSON that needs repair

        Object result = JsonRepair.repairJson(jsonStr, false, false, true);
        assertTrue(result instanceof JsonRepair.RepairResult);

        JsonRepair.RepairResult repairResult = (JsonRepair.RepairResult) result;

        // Test getter methods
        assertNotNull(repairResult.getResult());
        assertNotNull(repairResult.getLogs());
        assertTrue(repairResult.getLogs() instanceof List);

        // Test log entries
        List<JsonParser.LogEntry> logs = repairResult.getLogs();
        for (JsonParser.LogEntry log : logs) {
            assertNotNull(log.getText());
            // context may be null, so don't require non-null
        }
    }

    @Test
    public void testRepairJson_LargeDocument() {
        // Build a large JSON document
        StringBuilder largeJson = new StringBuilder("{");
        for (int i = 0; i < 100; i++) {
            if (i > 0) largeJson.append(",");
            largeJson.append("\"field").append(i).append("\":\"value").append(i).append("\"");
        }
        largeJson.append("}");

        String result = JsonRepair.repairJson(largeJson.toString());
        assertNotNull(result);
        try {
            objectMapper.readValue(result, Object.class);
        } catch (Exception e) {
            fail("Large JSON should be valid: " + e.getMessage());
        }
    }

    @Test
    public void testRepairJson_EmptyObjectsAndArrays() {
        // Test empty object
        String emptyObject = "{}";
        String result1 = JsonRepair.repairJson(emptyObject);
        assertEquals("{}", result1);

        // Test empty array
        String emptyArray = "[]";
        String result2 = JsonRepair.repairJson(emptyArray);
        assertEquals("[]", result2);
    }

    @Test
    public void testRepairJson_ParameterCombinations() {
        String jsonStr = "{\"name\":\"test\"}";

        // Test all parameters true combination
        Object result1 = JsonRepair.repairJson(jsonStr, true, true, true, true);
        assertNotNull(result1);
        assertTrue(result1 instanceof JsonRepair.RepairResult);

        // Test returnObjects=true, logging=false combination
        Object result2 = JsonRepair.repairJson(jsonStr, true, false, false, false);
        assertNotNull(result2);
        assertFalse(result2 instanceof JsonRepair.RepairResult);
        assertFalse(result2 instanceof String);
    }

    @Test
    public void testRepairJson_EdgeCases() {
        // Test whitespace-only string
        String whitespaceOnly = "   \t\n  ";
        String result1 = JsonRepair.repairJson(whitespaceOnly);
        assertNotNull(result1);

        // Test single character
        String singleChar = "a";
        String result2 = JsonRepair.repairJson(singleChar);
        assertNotNull(result2);

        // Test special symbols
        String specialChars = "{}[],:\"";
        String result3 = JsonRepair.repairJson(specialChars);
        assertNotNull(result3);
    }

    @Test
    public void testRepairJson_ReturnEmptyString() {
        // Test cases that return empty string
        String result1 = JsonRepair.repairJson(null);
        assertEquals("", result1);

        String result2 = JsonRepair.repairJson("");
        assertEquals("", result2);
    }

    @Test
    public void testRepairJson_LoggingAndReturnObjects() {
        String jsonStr = "{name: test}";

        // Test logging=true and returnObjects=true combination
        Object result = JsonRepair.repairJson(jsonStr, true, false, true);
        assertNotNull(result);
        assertTrue(result instanceof JsonRepair.RepairResult);

        JsonRepair.RepairResult repairResult = (JsonRepair.RepairResult) result;
        assertNotNull(repairResult.getResult());
        assertNotNull(repairResult.getLogs());
    }

    @Test
    public void testRepairResult_Constructor() {
        // Test RepairResult constructor
        String testResult = "test";
        List<JsonParser.LogEntry> testLogs = new java.util.ArrayList<>();
        testLogs.add(new JsonParser.LogEntry("test log", "test context"));

        JsonRepair.RepairResult repairResult = new JsonRepair.RepairResult(testResult, testLogs);

        assertEquals(testResult, repairResult.getResult());
        assertEquals(testLogs, repairResult.getLogs());
    }

    @Test
    public void testLogEntry_Constructor() {
        // Test LogEntry constructor
        String testText = "test text";
        String testContext = "test context";

        JsonParser.LogEntry logEntry = new JsonParser.LogEntry(testText, testContext);

        assertEquals(testText, logEntry.getText());
        assertEquals(testContext, logEntry.getContext());
    }

    @Test
    public void testLogEntry_NullContext() {
        // Test LogEntry with null context
        String testText = "test text";

        JsonParser.LogEntry logEntry = new JsonParser.LogEntry(testText, null);

        assertEquals(testText, logEntry.getText());
        assertNull(logEntry.getContext());
    }

    @Test
    public void testRepairJson_AllParametersFalse() {
        String jsonStr = "{\"name\":\"test\"}";

        // Test all parameters false case
        Object result = JsonRepair.repairJson(jsonStr, false, false, false, false);
        assertNotNull(result);
        assertTrue(result instanceof String);
    }

    @Test
    public void testRepairJson_MixedParameterCombinations() {
        String jsonStr = "{\"name\":\"test\"}";

        // Test various parameter combinations
        Object result1 = JsonRepair.repairJson(jsonStr, true, true, false, true);
        assertNotNull(result1);

        Object result2 = JsonRepair.repairJson(jsonStr, false, true, true, false);
        assertNotNull(result2);
        assertTrue(result2 instanceof JsonRepair.RepairResult);

        Object result3 = JsonRepair.repairJson(jsonStr, true, false, true, true);
        assertNotNull(result3);
        assertTrue(result3 instanceof JsonRepair.RepairResult);
    }

    @Test
    public void testRepairJson_InvalidJsonWithLogging() {
        // Test repair process logging for invalid JSON
        String invalidJson = "{name:value,missing:quote}";

        Object result = JsonRepair.repairJson(invalidJson, false, false, true);
        assertNotNull(result);
        assertTrue(result instanceof JsonRepair.RepairResult);

        JsonRepair.RepairResult repairResult = (JsonRepair.RepairResult) result;
        assertNotNull(repairResult.getResult());
        assertNotNull(repairResult.getLogs());
        // Should have repair logs
        assertFalse(repairResult.getLogs().isEmpty());
    }

    @Test
    public void testRepairJson_ValidJsonWithSkipLoads() {
        // Test valid JSON but skip standard parsing
        String validJson = "{\"name\":\"test\",\"value\":123}";

        Object result = JsonRepair.repairJson(validJson, false, true, false);
        assertNotNull(result);
        assertTrue(result instanceof String);

        // Verify result is valid JSON
        try {
            objectMapper.readValue((String) result, Object.class);
        } catch (Exception e) {
            fail("Result should be valid JSON: " + e.getMessage());
        }
    }
}
