package com.facishare.crm.task.sfa.bizfeature.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.facishare.crm.task.sfa.bizfeature.model.FeatureModel;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.facishare.crm.task.sfa.bizfeature.constant.FeatureConstants;
import com.facishare.crm.task.sfa.bizfeature.constant.ParseRuleConstants;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

@Component
public class ParseTagRule extends AbsParseRuleService {

    @Override
    public String getRuleType() {
        return ParseRuleConstants.CalcMethodType.TAG.getCalcMethodType();
    }

    @Override
    protected FeatureModel.ParseValueData getValue(User user, IObjectData feature, IObjectData rule, IObjectData afterData, IObjectDescribe dataDescribe) {
        FeatureModel.ParseExtData parseExtData = getExtData(afterData);
        if (parseExtData == null) {
            return null;
        }

        Map<String, List<FeatureModel.ParseTagData>> tagMap = parseExtData.getTagMap();
        List<String> tags = getTags(feature);
        List<FeatureModel.ParseTagData> tagDataList = new ArrayList<>();
        for (String tag : tags) {
            List<FeatureModel.ParseTagData> parseTagDataList = tagMap.get(tag);
            if (CollectionUtils.notEmpty(parseTagDataList)) {
                tagDataList.addAll(parseTagDataList);
            }
        }
        FeatureModel.ParseValueData ret = new FeatureModel.ParseValueData();
        ret.setValue(tagDataList);
        return ret;
    }

    @NotNull
    private List<String> getTags(IObjectData feature) {
        List<String> tags = Lists.newArrayList();
        String tagId = feature.get(FeatureConstants.TAG_ID, String.class);
        if (StringUtils.isNotBlank(tagId)) {
            tags.add(tagId);
        }else{
            tags.add(feature.get(FeatureConstants.DATA_SOURCE_THIRD, String.class));
        }
        return tags;
    }

    @Override
    protected boolean makeFeatureData(User user, IObjectData feature, IObjectData rule, IObjectData afterData,
            FeatureModel.FeatureData featureData, FeatureModel.ParseValueData value) {
        List<FeatureModel.ParseTagData> tagDataList = (List<FeatureModel.ParseTagData>) value.getValue();
        if (featureData.getReturnDataType().equals(ParseRuleConstants.ReturnDataType.TEXT.getReturnDataType())) {
            featureData.setValueText(tagDataList.get(0).getId());
        } else if (featureData.getReturnDataType().equals(ParseRuleConstants.ReturnDataType.BOOL.getReturnDataType())) {
            featureData.setValueBoolean(CollectionUtils.notEmpty(tagDataList));
        } else if (featureData.getReturnDataType()
                .equals(ParseRuleConstants.ReturnDataType.NUMERIC.getReturnDataType())) {
            featureData.setValueNumber((double) tagDataList.size());
        }
        setMasterInfo(user, feature, rule, afterData, featureData);
        return !StringUtils.isNotBlank(featureData.getMasterApiName()) || !StringUtils.isBlank(featureData.getMasterId());
    }
}
