package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 高级JSON修复服务
 * 提供更强大和可配置的JSON修复功能
 */
@Service
public class AdvancedJsonRepairService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdvancedJsonRepairService.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private JsonRepairConfig config = JsonRepairConfig.DEFAULT;
    
    // 修复策略枚举
    public enum RepairStrategy {
        QUICK,      // 快速修复
        DEEP,       // 深度修复
        AGGRESSIVE  // 激进修复
    }
    
    // 修复结果类
    public static class RepairResult {
        private final String originalJson;
        private final String repairedJson;
        private final boolean success;
        private final List<String> repairActions;
        private final Exception error;
        
        public RepairResult(String originalJson, String repairedJson, boolean success, 
                          List<String> repairActions, Exception error) {
            this.originalJson = originalJson;
            this.repairedJson = repairedJson;
            this.success = success;
            this.repairActions = repairActions != null ? new ArrayList<>(repairActions) : new ArrayList<>();
            this.error = error;
        }
        
        // Getters
        public String getOriginalJson() { return originalJson; }
        public String getRepairedJson() { return repairedJson; }
        public boolean isSuccess() { return success; }
        public List<String> getRepairActions() { return new ArrayList<>(repairActions); }
        public Exception getError() { return error; }
        
        @Override
        public String toString() {
            return "RepairResult{" +
                    "success=" + success +
                    ", repairActions=" + repairActions.size() +
                    ", error=" + (error != null ? error.getMessage() : "none") +
                    '}';
        }
    }
    
    /**
     * 设置修复配置
     */
    public void setConfig(JsonRepairConfig config) {
        this.config = config != null ? config : JsonRepairConfig.DEFAULT;
    }
    
    /**
     * 使用指定策略修复JSON
     */
    public RepairResult repairJson(String malformedJson, RepairStrategy strategy) {
        return repairJson(malformedJson, strategy, this.config);
    }
    
    /**
     * 使用指定策略和配置修复JSON
     */
    public RepairResult repairJson(String malformedJson, RepairStrategy strategy, JsonRepairConfig config) {
        List<String> repairActions = new ArrayList<>();
        
        if (malformedJson == null || malformedJson.trim().isEmpty()) {
            repairActions.add("处理空输入");
            return new RepairResult(malformedJson, "{}", true, repairActions, null);
        }
        
        // 检查长度限制
        if (malformedJson.length() > config.getMaxLength()) {
            String error = "JSON长度超过限制: " + malformedJson.length() + " > " + config.getMaxLength();
            return new RepairResult(malformedJson, config.isReturnEmptyOnFailure() ? "{}" : malformedJson, 
                                  false, repairActions, new JsonRepairException(error));
        }
        
        // 先检查是否已经是有效JSON
        if (isValidJson(malformedJson)) {
            repairActions.add("JSON已经有效，无需修复");
            return new RepairResult(malformedJson, malformedJson.trim(), true, repairActions, null);
        }
        
        String repairedJson = malformedJson.trim();
        Exception lastError = null;
        
        try {
            // 根据策略执行不同的修复流程
            switch (strategy) {
                case QUICK:
                    repairedJson = performQuickRepair(repairedJson, config, repairActions);
                    break;
                case DEEP:
                    repairedJson = performDeepRepair(repairedJson, config, repairActions);
                    break;
                case AGGRESSIVE:
                    repairedJson = performAggressiveRepair(repairedJson, config, repairActions);
                    break;
            }
            
            // 验证修复结果
            if (isValidJson(repairedJson)) {
                if (config.isLogRepairAttempts()) {
                    logger.info("JSON修复成功，策略: {}, 执行的修复操作: {}", strategy, repairActions);
                }
                return new RepairResult(malformedJson, repairedJson, true, repairActions, null);
            } else {
                lastError = new JsonRepairException("修复后的JSON仍然无效");
            }
            
        } catch (Exception e) {
            lastError = e;
            if (config.isLogRepairAttempts()) {
                logger.warn("JSON修复失败，策略: {}, 错误: {}", strategy, e.getMessage());
            }
        }
        
        // 修复失败的处理
        String fallbackJson = config.isReturnEmptyOnFailure() ? "{}" : malformedJson;
        repairActions.add("修复失败，返回" + (config.isReturnEmptyOnFailure() ? "空对象" : "原始JSON"));
        
        return new RepairResult(malformedJson, fallbackJson, false, repairActions, lastError);
    }
    
    /**
     * 快速修复
     */
    private String performQuickRepair(String json, JsonRepairConfig config, List<String> actions) {
        String result = json;
        
        if (config.isRemoveControlChars()) {
            String before = result;
            result = removeControlCharacters(result);
            if (!before.equals(result)) actions.add("移除控制字符");
        }
        
        if (config.isRemoveComments()) {
            String before = result;
            result = removeComments(result);
            if (!before.equals(result)) actions.add("移除注释");
        }
        
        if (config.isFixUnquotedKeys()) {
            String before = result;
            result = fixUnquotedKeys(result);
            if (!before.equals(result)) actions.add("修复未引用的键名");
        }
        
        if (config.isFixSingleQuotes()) {
            String before = result;
            result = fixSingleQuotes(result);
            if (!before.equals(result)) actions.add("修复单引号");
        }
        
        if (config.isRemoveTrailingCommas()) {
            String before = result;
            result = removeTrailingCommas(result);
            if (!before.equals(result)) actions.add("移除尾随逗号");
        }
        
        return result;
    }
    
    /**
     * 深度修复
     */
    private String performDeepRepair(String json, JsonRepairConfig config, List<String> actions) {
        String result = performQuickRepair(json, config, actions);
        
        if (config.isAddMissingCommas()) {
            String before = result;
            result = addMissingCommas(result);
            if (!before.equals(result)) actions.add("添加缺失的逗号");
        }
        
        if (config.isFixBrackets()) {
            String before = result;
            result = fixBracketMatching(result);
            if (!before.equals(result)) actions.add("修复括号匹配");
        }
        
        if (config.isHandleTruncation()) {
            String before = result;
            result = handleTruncation(result);
            if (!before.equals(result)) actions.add("处理截断问题");
        }
        
        if (config.isFixValueTypes()) {
            String before = result;
            result = fixValueTypes(result);
            if (!before.equals(result)) actions.add("修复值类型");
        }
        
        return result;
    }
    
    /**
     * 激进修复
     */
    private String performAggressiveRepair(String json, JsonRepairConfig config, List<String> actions) {
        String result = performDeepRepair(json, config, actions);
        
        // 如果深度修复失败，尝试更激进的方法
        if (!isValidJson(result)) {
            actions.add("尝试激进修复");
            
            // 尝试重构JSON
            result = attemptJsonReconstruction(result, actions);
            
            // 如果仍然失败，尝试提取有效部分
            if (!isValidJson(result)) {
                result = extractValidParts(result, actions);
            }
        }
        
        return result;
    }
    
    /**
     * 尝试重构JSON
     */
    private String attemptJsonReconstruction(String json, List<String> actions) {
        try {
            // 提取所有可能的键值对
            Map<String, String> keyValues = extractKeyValuePairs(json);
            
            if (!keyValues.isEmpty()) {
                StringBuilder reconstructed = new StringBuilder("{");
                boolean first = true;
                
                for (Map.Entry<String, String> entry : keyValues.entrySet()) {
                    if (!first) reconstructed.append(",");
                    reconstructed.append("\"").append(escapeJsonString(entry.getKey())).append("\":")
                               .append("\"").append(escapeJsonString(entry.getValue())).append("\"");
                    first = false;
                }
                
                reconstructed.append("}");
                actions.add("重构JSON结构");
                return reconstructed.toString();
            }
        } catch (Exception e) {
            logger.debug("JSON重构失败: {}", e.getMessage());
        }
        
        return json;
    }
    
    /**
     * 提取有效部分
     */
    private String extractValidParts(String json, List<String> actions) {
        try {
            // 尝试找到最大的有效JSON片段
            for (int i = json.length(); i > 0; i--) {
                String substring = json.substring(0, i);
                
                // 尝试补全括号
                String completed = completeJson(substring);
                
                if (isValidJson(completed)) {
                    actions.add("提取有效JSON片段 (长度: " + i + ")");
                    return completed;
                }
            }
        } catch (Exception e) {
            logger.debug("提取有效部分失败: {}", e.getMessage());
        }
        
        actions.add("无法提取有效部分，返回空对象");
        return "{}";
    }
    
    /**
     * 补全JSON结构
     */
    private String completeJson(String json) {
        StringBuilder result = new StringBuilder(json.trim());
        
        // 移除末尾的逗号
        while (result.length() > 0 && result.charAt(result.length() - 1) == ',') {
            result.deleteCharAt(result.length() - 1);
        }
        
        // 计算需要的闭合括号
        int braceCount = 0;
        int bracketCount = 0;
        boolean inString = false;
        boolean escaped = false;
        
        for (int i = 0; i < result.length(); i++) {
            char c = result.charAt(i);
            
            if (escaped) {
                escaped = false;
                continue;
            }
            
            if (c == '\\' && inString) {
                escaped = true;
                continue;
            }
            
            if (c == '"') {
                inString = !inString;
                continue;
            }
            
            if (!inString) {
                switch (c) {
                    case '{': braceCount++; break;
                    case '}': braceCount--; break;
                    case '[': bracketCount++; break;
                    case ']': bracketCount--; break;
                }
            }
        }
        
        // 如果在字符串中结束，添加结束引号
        if (inString) {
            result.append('"');
        }
        
        // 添加缺失的闭合括号
        while (bracketCount > 0) {
            result.append(']');
            bracketCount--;
        }
        
        while (braceCount > 0) {
            result.append('}');
            braceCount--;
        }
        
        return result.toString();
    }
    
    // 以下是各种修复方法的实现...
    private String removeControlCharacters(String json) {
        return json.replaceAll("[\\x00-\\x1F\\x7F]", "");
    }
    
    private String removeComments(String json) {
        // 移除 /* */ 注释
        json = json.replaceAll("/\\*.*?\\*/", "");
        // 移除 // 注释
        json = json.replaceAll("//.*?(?=\\n|$)", "");
        return json;
    }
    
    private String fixUnquotedKeys(String json) {
        Pattern pattern = Pattern.compile("([{,]\\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:");
        return pattern.matcher(json).replaceAll("$1\"$2\":");
    }
    
    private String fixSingleQuotes(String json) {
        return json.replaceAll("'([^']*)'", "\"$1\"");
    }
    
    private String removeTrailingCommas(String json) {
        return json.replaceAll(",\\s*([}\\]])", "$1");
    }
    
    private String addMissingCommas(String json) {
        Pattern pattern = Pattern.compile("([\"'}\\]\\d])\\s*([\"'{\\[])");
        return pattern.matcher(json).replaceAll("$1,$2");
    }
    
    private String fixBracketMatching(String json) {
        // 这里使用简化版本，实际实现会更复杂
        return JsonRepairUtil.quickRepair(json);
    }
    
    private String handleTruncation(String json) {
        json = json.trim();
        if (json.endsWith(",")) {
            json = json.substring(0, json.length() - 1);
        }
        return json;
    }
    
    private String fixValueTypes(String json) {
        return json.replaceAll("\\bTRUE\\b", "true")
                  .replaceAll("\\bFALSE\\b", "false")
                  .replaceAll("\\bNULL\\b", "null");
    }
    
    private Map<String, String> extractKeyValuePairs(String json) {
        Map<String, String> pairs = new LinkedHashMap<>();
        Pattern pattern = Pattern.compile("\"([^\"]+)\"\\s*:\\s*\"([^\"]+)\"");
        Matcher matcher = pattern.matcher(json);
        
        while (matcher.find()) {
            pairs.put(matcher.group(1), matcher.group(2));
        }
        
        return pairs;
    }
    
    private String escapeJsonString(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
    
    private boolean isValidJson(String json) {
        try {
            objectMapper.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 便捷方法：使用默认配置和深度修复策略
     */
    public String repairJsonString(String malformedJson) {
        RepairResult result = repairJson(malformedJson, RepairStrategy.DEEP);
        return result.getRepairedJson();
    }
    
    /**
     * 便捷方法：安全解析JSON
     */
    public JsonNode safeParseJson(String jsonString) {
        RepairResult result = repairJson(jsonString, RepairStrategy.DEEP);
        if (result.isSuccess()) {
            try {
                return objectMapper.readTree(result.getRepairedJson());
            } catch (JsonProcessingException e) {
                logger.error("解析修复后的JSON失败: {}", e.getMessage());
            }
        }
        
        // 返回空对象
        try {
            return objectMapper.readTree("{}");
        } catch (JsonProcessingException e) {
            return null;
        }
    }
}