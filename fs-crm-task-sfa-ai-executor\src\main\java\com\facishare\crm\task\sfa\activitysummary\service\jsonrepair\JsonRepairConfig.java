package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair;

/**
 * JSON修复配置类
 * 用于配置JSON修复的各种选项
 */
public class JsonRepairConfig {
    
    // 修复选项
    private boolean fixUnquotedKeys = true;          // 修复未引用的键名
    private boolean fixSingleQuotes = true;         // 修复单引号
    private boolean removeTrailingCommas = true;    // 移除尾随逗号
    private boolean addMissingCommas = true;        // 添加缺失的逗号
    private boolean removeComments = true;          // 移除注释
    private boolean fixBrackets = true;             // 修复括号匹配
    private boolean handleTruncation = true;        // 处理截断
    private boolean removeControlChars = true;      // 移除控制字符
    private boolean fixValueTypes = true;           // 修复值类型
    
    // 限制选项
    private int maxDepth = 100;                     // 最大嵌套深度
    private int maxLength = 1000000;                // 最大JSON长度
    private int maxRepairAttempts = 3;              // 最大修复尝试次数
    
    // 错误处理选项
    private boolean strictMode = false;             // 严格模式
    private boolean returnEmptyOnFailure = true;    // 失败时返回空对象
    private boolean logRepairAttempts = true;       // 记录修复尝试
    
    // 默认配置
    public static final JsonRepairConfig DEFAULT = new JsonRepairConfig();
    
    // 严格配置 - 只进行安全的修复
    public static final JsonRepairConfig STRICT = new JsonRepairConfig()
            .setStrictMode(true)
            .setAddMissingCommas(false)
            .setHandleTruncation(false);
    
    // 宽松配置 - 尝试所有可能的修复
    public static final JsonRepairConfig LENIENT = new JsonRepairConfig()
            .setMaxRepairAttempts(5)
            .setReturnEmptyOnFailure(true);
    
    public JsonRepairConfig() {
        // 使用默认配置
    }
    
    // Getters
    public boolean isFixUnquotedKeys() { return fixUnquotedKeys; }
    public boolean isFixSingleQuotes() { return fixSingleQuotes; }
    public boolean isRemoveTrailingCommas() { return removeTrailingCommas; }
    public boolean isAddMissingCommas() { return addMissingCommas; }
    public boolean isRemoveComments() { return removeComments; }
    public boolean isFixBrackets() { return fixBrackets; }
    public boolean isHandleTruncation() { return handleTruncation; }
    public boolean isRemoveControlChars() { return removeControlChars; }
    public boolean isFixValueTypes() { return fixValueTypes; }
    public int getMaxDepth() { return maxDepth; }
    public int getMaxLength() { return maxLength; }
    public int getMaxRepairAttempts() { return maxRepairAttempts; }
    public boolean isStrictMode() { return strictMode; }
    public boolean isReturnEmptyOnFailure() { return returnEmptyOnFailure; }
    public boolean isLogRepairAttempts() { return logRepairAttempts; }
    
    // Setters (返回this以支持链式调用)
    public JsonRepairConfig setFixUnquotedKeys(boolean fixUnquotedKeys) {
        this.fixUnquotedKeys = fixUnquotedKeys;
        return this;
    }
    
    public JsonRepairConfig setFixSingleQuotes(boolean fixSingleQuotes) {
        this.fixSingleQuotes = fixSingleQuotes;
        return this;
    }
    
    public JsonRepairConfig setRemoveTrailingCommas(boolean removeTrailingCommas) {
        this.removeTrailingCommas = removeTrailingCommas;
        return this;
    }
    
    public JsonRepairConfig setAddMissingCommas(boolean addMissingCommas) {
        this.addMissingCommas = addMissingCommas;
        return this;
    }
    
    public JsonRepairConfig setRemoveComments(boolean removeComments) {
        this.removeComments = removeComments;
        return this;
    }
    
    public JsonRepairConfig setFixBrackets(boolean fixBrackets) {
        this.fixBrackets = fixBrackets;
        return this;
    }
    
    public JsonRepairConfig setHandleTruncation(boolean handleTruncation) {
        this.handleTruncation = handleTruncation;
        return this;
    }
    
    public JsonRepairConfig setRemoveControlChars(boolean removeControlChars) {
        this.removeControlChars = removeControlChars;
        return this;
    }
    
    public JsonRepairConfig setFixValueTypes(boolean fixValueTypes) {
        this.fixValueTypes = fixValueTypes;
        return this;
    }
    
    public JsonRepairConfig setMaxDepth(int maxDepth) {
        this.maxDepth = maxDepth;
        return this;
    }
    
    public JsonRepairConfig setMaxLength(int maxLength) {
        this.maxLength = maxLength;
        return this;
    }
    
    public JsonRepairConfig setMaxRepairAttempts(int maxRepairAttempts) {
        this.maxRepairAttempts = maxRepairAttempts;
        return this;
    }
    
    public JsonRepairConfig setStrictMode(boolean strictMode) {
        this.strictMode = strictMode;
        return this;
    }
    
    public JsonRepairConfig setReturnEmptyOnFailure(boolean returnEmptyOnFailure) {
        this.returnEmptyOnFailure = returnEmptyOnFailure;
        return this;
    }
    
    public JsonRepairConfig setLogRepairAttempts(boolean logRepairAttempts) {
        this.logRepairAttempts = logRepairAttempts;
        return this;
    }
    
    /**
     * 创建配置的副本
     */
    public JsonRepairConfig copy() {
        return new JsonRepairConfig()
                .setFixUnquotedKeys(this.fixUnquotedKeys)
                .setFixSingleQuotes(this.fixSingleQuotes)
                .setRemoveTrailingCommas(this.removeTrailingCommas)
                .setAddMissingCommas(this.addMissingCommas)
                .setRemoveComments(this.removeComments)
                .setFixBrackets(this.fixBrackets)
                .setHandleTruncation(this.handleTruncation)
                .setRemoveControlChars(this.removeControlChars)
                .setFixValueTypes(this.fixValueTypes)
                .setMaxDepth(this.maxDepth)
                .setMaxLength(this.maxLength)
                .setMaxRepairAttempts(this.maxRepairAttempts)
                .setStrictMode(this.strictMode)
                .setReturnEmptyOnFailure(this.returnEmptyOnFailure)
                .setLogRepairAttempts(this.logRepairAttempts);
    }
    
    @Override
    public String toString() {
        return "JsonRepairConfig{" +
                "fixUnquotedKeys=" + fixUnquotedKeys +
                ", fixSingleQuotes=" + fixSingleQuotes +
                ", removeTrailingCommas=" + removeTrailingCommas +
                ", addMissingCommas=" + addMissingCommas +
                ", removeComments=" + removeComments +
                ", fixBrackets=" + fixBrackets +
                ", handleTruncation=" + handleTruncation +
                ", removeControlChars=" + removeControlChars +
                ", fixValueTypes=" + fixValueTypes +
                ", maxDepth=" + maxDepth +
                ", maxLength=" + maxLength +
                ", maxRepairAttempts=" + maxRepairAttempts +
                ", strictMode=" + strictMode +
                ", returnEmptyOnFailure=" + returnEmptyOnFailure +
                ", logRepairAttempts=" + logRepairAttempts +
                '}';
    }
}