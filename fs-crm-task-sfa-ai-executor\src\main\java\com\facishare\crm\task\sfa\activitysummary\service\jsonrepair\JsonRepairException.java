package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair;

import lombok.Getter;

/**
 * JSON修复相关异常
 */
@Getter
public class JsonRepairException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    private final String originalJson;
    private final String repairAttempt;
    
    public JsonRepairException(String message) {
        super(message);
        this.originalJson = null;
        this.repairAttempt = null;
    }
    
    public JsonRepairException(String message, Throwable cause) {
        super(message, cause);
        this.originalJson = null;
        this.repairAttempt = null;
    }
    
    public JsonRepairException(String message, String originalJson, String repairAttempt) {
        super(message);
        this.originalJson = originalJson;
        this.repairAttempt = repairAttempt;
    }
    
    public JsonRepairException(String message, String originalJson, String repairAttempt, Throwable cause) {
        super(message, cause);
        this.originalJson = originalJson;
        this.repairAttempt = repairAttempt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(super.toString());
        
        if (originalJson != null) {
            sb.append("\nOriginal JSON: ").append(truncate(originalJson, 200));
        }
        
        if (repairAttempt != null) {
            sb.append("\nRepair Attempt: ").append(truncate(repairAttempt, 200));
        }
        
        return sb.toString();
    }
    
    private String truncate(String str, int maxLength) {
        if (str == null) return null;
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength) + "...";
    }
}