package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * JSON修复服务
 * 用于修复格式错误或损坏的JSON数据
 */
@Service
public class JsonRepairService {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonRepairService.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 常见的JSON修复模式
    private static final Pattern UNQUOTED_KEY_PATTERN = Pattern.compile("([{,]\\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:");
    private static final Pattern SINGLE_QUOTE_PATTERN = Pattern.compile("'([^']*)'");
    private static final Pattern TRAILING_COMMA_PATTERN = Pattern.compile(",\\s*([}\\]])");
    private static final Pattern MISSING_COMMA_PATTERN = Pattern.compile("([\"'}\\]\\d])\\s*([\"'{\\[])");
    private static final Pattern CONTROL_CHARS_PATTERN = Pattern.compile("[\\x00-\\x1F\\x7F]");
    
    /**
     * 修复JSON字符串
     * @param malformedJson 损坏的JSON字符串
     * @return 修复后的JSON字符串
     */
    public String repairJson(String malformedJson) {
        if (malformedJson == null || malformedJson.trim().isEmpty()) {
            return "{}";
        }
        
        try {
            // 先尝试直接解析，如果成功则无需修复
            objectMapper.readTree(malformedJson);
            return malformedJson;
        } catch (JsonProcessingException e) {
            logger.debug("JSON解析失败，开始修复: {}", e.getMessage());
        }
        
        String repairedJson = malformedJson.trim();
        
        // 1. 移除控制字符
        repairedJson = removeControlCharacters(repairedJson);
        
        // 2. 修复未引用的键名
        repairedJson = fixUnquotedKeys(repairedJson);
        
        // 3. 修复单引号为双引号
        repairedJson = fixSingleQuotes(repairedJson);
        
        // 4. 移除尾随逗号
        repairedJson = removeTrailingCommas(repairedJson);
        
        // 5. 添加缺失的逗号
        repairedJson = addMissingCommas(repairedJson);
        
        // 6. 修复不匹配的括号
        repairedJson = fixMismatchedBrackets(repairedJson);
        
        // 7. 处理截断的JSON
        repairedJson = fixTruncatedJson(repairedJson);
        
        // 验证修复结果
        try {
            objectMapper.readTree(repairedJson);
            logger.info("JSON修复成功");
            return repairedJson;
        } catch (JsonProcessingException e) {
            logger.warn("JSON修复失败，返回空对象: {}", e.getMessage());
            return "{}";
        }
    }
    
    /**
     * 移除控制字符
     */
    private String removeControlCharacters(String json) {
        return CONTROL_CHARS_PATTERN.matcher(json).replaceAll("");
    }
    
    /**
     * 修复未引用的键名
     * 例如: {name: "John"} -> {"name": "John"}
     */
    private String fixUnquotedKeys(String json) {
        return UNQUOTED_KEY_PATTERN.matcher(json).replaceAll("$1\"$2\":");
    }
    
    /**
     * 修复单引号为双引号
     * 例如: {'name': 'John'} -> {"name": "John"}
     */
    private String fixSingleQuotes(String json) {
        return SINGLE_QUOTE_PATTERN.matcher(json).replaceAll("\"$1\"");
    }
    
    /**
     * 移除尾随逗号
     * 例如: {"name": "John",} -> {"name": "John"}
     */
    private String removeTrailingCommas(String json) {
        return TRAILING_COMMA_PATTERN.matcher(json).replaceAll("$1");
    }
    
    /**
     * 添加缺失的逗号
     * 例如: {"name": "John" "age": 30} -> {"name": "John", "age": 30}
     */
    private String addMissingCommas(String json) {
        return MISSING_COMMA_PATTERN.matcher(json).replaceAll("$1,$2");
    }
    
    /**
     * 修复不匹配的括号
     */
    private String fixMismatchedBrackets(String json) {
        StringBuilder result = new StringBuilder();
        int braceCount = 0;
        int bracketCount = 0;
        
        for (char c : json.toCharArray()) {
            result.append(c);
            
            switch (c) {
                case '{':
                    braceCount++;
                    break;
                case '}':
                    braceCount--;
                    break;
                case '[':
                    bracketCount++;
                    break;
                case ']':
                    bracketCount--;
                    break;
            }
        }
        
        // 补充缺失的闭合括号
        while (braceCount > 0) {
            result.append('}');
            braceCount--;
        }
        
        while (bracketCount > 0) {
            result.append(']');
            bracketCount--;
        }
        
        return result.toString();
    }
    
    /**
     * 修复截断的JSON
     */
    private String fixTruncatedJson(String json) {
        json = json.trim();
        
        // 如果JSON以逗号结尾，移除它
        if (json.endsWith(",")) {
            json = json.substring(0, json.length() - 1);
        }
        
        // 如果JSON没有正确结束，尝试补全
        if (!json.endsWith("}") && !json.endsWith("]") && !json.endsWith("\"") && !json.endsWith("null") && !json.endsWith("true") && !json.endsWith("false") && !json.matches(".*\\d$")) {
            // 尝试添加引号结束未完成的字符串
            if (json.lastIndexOf("\"") > json.lastIndexOf("\":")) {
                json += "\"";
            }
        }
        
        return json;
    }
    
    /**
     * 安全地解析JSON，返回JsonNode
     * @param jsonString JSON字符串
     * @return JsonNode对象，如果解析失败返回null
     */
    public JsonNode safeParseJson(String jsonString) {
        try {
            String repairedJson = repairJson(jsonString);
            return objectMapper.readTree(repairedJson);
        } catch (Exception e) {
            logger.error("JSON解析失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证JSON字符串是否有效
     * @param jsonString JSON字符串
     * @return true if valid, false otherwise
     */
    public boolean isValidJson(String jsonString) {
        try {
            objectMapper.readTree(jsonString);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
}