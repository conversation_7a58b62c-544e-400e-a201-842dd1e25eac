package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.parsers;


import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.Constants;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.ContextValues;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonParser;

import java.util.ArrayList;
import java.util.List;

public class ArrayParser implements JsonElementParser {
    
    @Override
    public Object parse(JsonParser parser) {
        List<Object> arr = new ArrayList<>();
        parser.getContext().set(ContextValues.ARRAY);
        
        // Stop when you either find the closing bracket or reach end of string
        Character ch = parser.getCharAt();
        while (ch != null && ch != Constants.ARRAY_END && ch != Constants.OBJECT_END) {
            parser.skipWhitespacesAt();
            
            Object value = null;
            ch = parser.getCharAt();
            
            if (ch != null && Constants.STRING_DELIMITERS.contains(ch)) {
                // Sometimes LLMs forget to start an object and you think it's a string in an array
                // Check if this string is followed by a : to determine if it's an object
                int i = 1;
                i = parser.skipToCharacter(ch, i);
                i = parser.skipWhitespacesAt(i + 1, false);
                
                Character nextChar = parser.getCharAt(i);
                if (nextChar != null && nextChar == Constants.COLON) {
                    value = new ObjectParser().parse(parser);
                } else {
                    value = new StringParser().parse(parser);
                }
            } else if (ch != null) {
                value = parseJsonValue(parser);
            }
            
            // If parse_json() returns nothing valid, increase index by 1
            if (isStrictlyEmpty(value)) {
                parser.setIndex(parser.getIndex() + 1);
            } else if ("...".equals(value) && parser.getIndex() > 0) {
                // Check if previous character was '.'
                try {
                    if (parser.getJsonStr().charAt(parser.getIndex() - 1) == '.') {
                        parser.log("While parsing an array, found a stray '...'; ignoring it");
                    } else {
                        arr.add(value);
                    }
                } catch (IndexOutOfBoundsException e) {
                    arr.add(value);
                }
            } else {
                arr.add(value);
            }
            
            // Skip over whitespace and commas after a value but before closing ]
            ch = parser.getCharAt();
            while (ch != null && ch != Constants.ARRAY_END && 
                   (Character.isWhitespace(ch) || ch == Constants.COMMA)) {
                parser.setIndex(parser.getIndex() + 1);
                ch = parser.getCharAt();
            }
        }
        
        // Handle missing closing bracket
        if (ch != null && ch != Constants.ARRAY_END) {
            parser.log("While parsing an array we missed the closing ], ignoring it");
        }
        
        // Skip the closing bracket if present
        if (ch != null && ch == Constants.ARRAY_END) {
            parser.setIndex(parser.getIndex() + 1);
        }
        
        parser.getContext().reset();
        return arr;
    }
    
    private Object parseJsonValue(JsonParser parser) {
        Character ch = parser.getCharAt();
        if (ch == null) {
            return "";
        }
        
        char c = ch;
        
        // <object> starts with '{'
        if (c == Constants.OBJECT_START) {
            parser.setIndex(parser.getIndex() + 1);
            return new ObjectParser().parse(parser);
        }
        // <array> starts with '['
        else if (c == Constants.ARRAY_START) {
            parser.setIndex(parser.getIndex() + 1);
            return new ArrayParser().parse(parser);
        }
        // <string> starts with a quote or letter
        else if (Constants.STRING_DELIMITERS.contains(c) || Character.isLetter(c)) {
            return new StringParser().parse(parser);
        }
        // <number> starts with [0-9] or minus
        else if (Character.isDigit(c) || c == '-' || c == '.') {
            return new NumberParser().parse(parser);
        }
        else if (c == Constants.COMMENT_HASH || c == Constants.COMMENT_SLASH) {
            return new CommentParser().parse(parser);
        }
        else {
            // Skip unknown character and return empty string
            parser.setIndex(parser.getIndex() + 1);
            return "";
        }
    }
    
    private boolean isStrictlyEmpty(Object value) {
        return value == null || 
               (value instanceof String && ((String) value).isEmpty()) ||
               (value instanceof List && ((List<?>) value).isEmpty());
    }
}