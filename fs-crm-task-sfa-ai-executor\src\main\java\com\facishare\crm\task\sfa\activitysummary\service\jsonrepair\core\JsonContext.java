package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core;

import lombok.Getter;

import java.util.ArrayDeque;
import java.util.Deque;

public class JsonContext {
    private final Deque<ContextValues> context;
    @Getter
    private ContextValues current;
    @Getter
    private boolean empty;

    public JsonContext() {
        this.context = new ArrayDeque<>();
        this.current = null;
        this.empty = true;
    }

    /**
     * Set a new context value.
     *
     * @param value The context value to be added.
     */
    public void set(ContextValues value) {
        context.push(value);
        current = value;
        empty = false;
    }

    /**
     * Remove the most recent context value.
     */
    public void reset() {
        if (!context.isEmpty()) {
            context.pop();
            current = context.isEmpty() ? null : context.peek();
            empty = context.isEmpty();
        } else {
            current = null;
            empty = true;
        }
    }

    public int size() {
        return context.size();
    }
}