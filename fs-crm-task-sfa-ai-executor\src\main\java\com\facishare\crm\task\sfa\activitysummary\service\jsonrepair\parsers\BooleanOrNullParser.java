package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.parsers;


import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.Constants;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonParser;

public class BooleanOrNullParser implements JsonElementParser {
    
    @Override
    public Object parse(JsonParser parser) {
        int startingIndex = parser.getIndex();
        Character ch = parser.getCharAt();
        
        if (ch == null) {
            return Constants.EMPTY_STRING;
        }
        
        char c = Character.toLowerCase(ch);
        String targetLiteral = null;
        Object targetValue = null;
        
        if (c == 't') {
            targetLiteral = Constants.TRUE_LITERAL;
            targetValue = Boolean.TRUE;
        } else if (c == 'f') {
            targetLiteral = Constants.FALSE_LITERAL;
            targetValue = Boolean.FALSE;
        } else if (c == 'n') {
            targetLiteral = Constants.NULL_LITERAL;
        }
        
        if (targetLiteral != null) {
            int i = 0;
            while (ch != null && i < targetLiteral.length() && 
                   Character.toLowerCase(ch) == targetLiteral.charAt(i)) {
                i++;
                parser.setIndex(parser.getIndex() + 1);
                ch = parser.getCharAt();
            }
            
            if (i == targetLiteral.length()) {
                return targetValue;
            }
        }
        
        // If nothing works, reset the index before returning
        parser.setIndex(startingIndex);
        return Constants.EMPTY_STRING;
    }
}