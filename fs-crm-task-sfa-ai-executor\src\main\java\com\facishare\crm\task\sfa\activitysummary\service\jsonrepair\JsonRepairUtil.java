package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * JSON修复工具类
 * 提供静态方法用于修复各种JSON格式问题
 */
public class JsonRepairUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonRepairUtil.class);
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    // 修复模式定义
    private static final Pattern[] REPAIR_PATTERNS = {
        // 1. 未引用的键名: {name: "value"} -> {"name": "value"}
        Pattern.compile("([{,]\\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:"),
        
        // 2. 单引号: {'name': 'value'} -> {"name": "value"}
        Pattern.compile("'([^']*)'"),
        
        // 3. 尾随逗号: {"name": "value",} -> {"name": "value"}
        Pattern.compile(",\\s*([}\\]])"),
        
        // 4. 缺失逗号: {"a":"b""c":"d"} -> {"a":"b","c":"d"}
        Pattern.compile("([\"'}\\]\\d])\\s*([\"'{\\[])"),
        
        // 5. JavaScript注释: /* comment */ 或 // comment
        Pattern.compile("/\\*.*?\\*/|//.*?(?=\\n|$)", Pattern.DOTALL),
        
        // 6. 多余的分号: {"name": "value";} -> {"name": "value"}
        Pattern.compile(";\\s*([}\\]])"),
        
        // 7. 等号替换冒号: {"name" = "value"} -> {"name": "value"}
        Pattern.compile("([\"'])\\s*=\\s*"),
        
        // 8. 控制字符
        Pattern.compile("[\\x00-\\x1F\\x7F]")
    };
    
    /**
     * 快速修复JSON字符串
     * @param malformedJson 损坏的JSON
     * @return 修复后的JSON字符串
     */
    public static String quickRepair(String malformedJson) {
        if (isNullOrEmpty(malformedJson)) {
            return "{}";
        }
        
        // 先检查是否已经是有效JSON
        if (isValidJson(malformedJson)) {
            return malformedJson.trim();
        }
        
        String repaired = malformedJson.trim();
        
        // 应用基础修复规则
        repaired = applyBasicRepairs(repaired);
        
        // 修复括号匹配
        repaired = fixBracketMatching(repaired);
        
        // 处理截断问题
        repaired = handleTruncation(repaired);
        
        return repaired;
    }
    
    /**
     * 深度修复JSON字符串
     * @param malformedJson 损坏的JSON
     * @return 修复后的JSON字符串
     */
    public static String deepRepair(String malformedJson) {
        if (isNullOrEmpty(malformedJson)) {
            return "{}";
        }
        
        String repaired = quickRepair(malformedJson);
        
        // 如果快速修复成功，直接返回
        if (isValidJson(repaired)) {
            return repaired;
        }
        
        // 尝试更复杂的修复策略
        repaired = fixComplexIssues(repaired);
        
        // 最后的验证和清理
        repaired = finalValidationAndCleanup(repaired);
        
        return repaired;
    }
    
    /**
     * 应用基础修复规则
     */
    private static String applyBasicRepairs(String json) {
        String result = json;
        
        // 1. 移除控制字符
        result = REPAIR_PATTERNS[7].matcher(result).replaceAll("");
        
        // 2. 移除注释
        result = REPAIR_PATTERNS[4].matcher(result).replaceAll("");
        
        // 3. 修复未引用的键名
        result = REPAIR_PATTERNS[0].matcher(result).replaceAll("$1\"$2\":");
        
        // 4. 修复单引号
        result = REPAIR_PATTERNS[1].matcher(result).replaceAll("\"$1\"");
        
        // 5. 移除尾随逗号
        result = REPAIR_PATTERNS[2].matcher(result).replaceAll("$1");
        
        // 6. 添加缺失的逗号
        result = REPAIR_PATTERNS[3].matcher(result).replaceAll("$1,$2");
        
        // 7. 修复多余分号
        result = REPAIR_PATTERNS[5].matcher(result).replaceAll("$1");
        
        // 8. 修复等号
        result = REPAIR_PATTERNS[6].matcher(result).replaceAll("$1:");
        
        return result;
    }
    
    /**
     * 修复括号匹配问题
     */
    private static String fixBracketMatching(String json) {
        StringBuilder result = new StringBuilder();
        Stack<Character> bracketStack = new Stack<>();
        
        boolean inString = false;
        boolean escaped = false;
        
        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            
            if (escaped) {
                escaped = false;
                result.append(c);
                continue;
            }
            
            if (c == '\\' && inString) {
                escaped = true;
                result.append(c);
                continue;
            }
            
            if (c == '"') {
                inString = !inString;
                result.append(c);
                continue;
            }
            
            if (!inString) {
                switch (c) {
                    case '{':
                    case '[':
                        bracketStack.push(c);
                        result.append(c);
                        break;
                    case '}':
                        if (!bracketStack.isEmpty() && bracketStack.peek() == '{') {
                            bracketStack.pop();
                        }
                        result.append(c);
                        break;
                    case ']':
                        if (!bracketStack.isEmpty() && bracketStack.peek() == '[') {
                            bracketStack.pop();
                        }
                        result.append(c);
                        break;
                    default:
                        result.append(c);
                        break;
                }
            } else {
                result.append(c);
            }
        }
        
        // 补充缺失的闭合括号
        while (!bracketStack.isEmpty()) {
            char openBracket = bracketStack.pop();
            char closeBracket = (openBracket == '{') ? '}' : ']';
            result.append(closeBracket);
        }
        
        return result.toString();
    }
    
    /**
     * 处理JSON截断问题
     */
    private static String handleTruncation(String json) {
        json = json.trim();
        
        // 移除末尾的逗号
        if (json.endsWith(",")) {
            json = json.substring(0, json.length() - 1).trim();
        }
        
        // 处理未完成的字符串
        int lastQuoteIndex = json.lastIndexOf('"');
        int lastColonIndex = json.lastIndexOf(':');
        
        if (lastColonIndex > lastQuoteIndex && !json.endsWith("\"") && !json.endsWith("}") && !json.endsWith("]")) {
            // 可能是未完成的字符串值
            if (!json.matches(".*\\d$") && !json.endsWith("true") && !json.endsWith("false") && !json.endsWith("null")) {
                json += "\"";
            }
        }
        
        return json;
    }
    
    /**
     * 修复复杂问题
     */
    private static String fixComplexIssues(String json) {
        // 尝试修复嵌套结构问题
        json = fixNestedStructures(json);
        
        // 修复数组问题
        json = fixArrayIssues(json);
        
        // 修复值类型问题
        json = fixValueTypes(json);
        
        return json;
    }
    
    /**
     * 修复嵌套结构问题
     */
    private static String fixNestedStructures(String json) {
        // 处理嵌套对象中的问题
        StringBuilder result = new StringBuilder();
        boolean inString = false;
        boolean escaped = false;
        
        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            
            if (escaped) {
                escaped = false;
                result.append(c);
                continue;
            }
            
            if (c == '\\' && inString) {
                escaped = true;
                result.append(c);
                continue;
            }
            
            if (c == '"') {
                inString = !inString;
            }
            
            result.append(c);
        }
        
        return result.toString();
    }
    
    /**
     * 修复数组问题
     */
    private static String fixArrayIssues(String json) {
        // 修复数组中的逗号问题
        Pattern arrayCommaPattern = Pattern.compile("(\\[\\s*),|(,\\s*,)|(,\\s*\\])");
        Matcher matcher = arrayCommaPattern.matcher(json);
        
        while (matcher.find()) {
            if (matcher.group().matches("\\[\\s*,")) {
                json = json.replace(matcher.group(), "[");
            } else if (matcher.group().matches(",\\s*,")) {
                json = json.replace(matcher.group(), ",");
            } else if (matcher.group().matches(",\\s*\\]")) {
                json = json.replace(matcher.group(), "]");
            }
        }
        
        return json;
    }
    
    /**
     * 修复值类型问题
     */
    private static String fixValueTypes(String json) {
        // 修复布尔值大小写问题
        json = json.replaceAll("\\bTRUE\\b", "true")
                  .replaceAll("\\bFALSE\\b", "false")
                  .replaceAll("\\bNULL\\b", "null");
        
        // 修复数字格式问题
        json = json.replaceAll("\\b0+(\\d+)", "$1"); // 移除前导零
        
        return json;
    }
    
    /**
     * 最终验证和清理
     */
    private static String finalValidationAndCleanup(String json) {
        // 如果修复后仍然无效，尝试最基础的修复
        if (!isValidJson(json)) {
            // 提取所有可能的键值对
            List<String> keyValuePairs = extractKeyValuePairs(json);
            
            if (!keyValuePairs.isEmpty()) {
                StringBuilder rebuilt = new StringBuilder("{");
                for (int i = 0; i < keyValuePairs.size(); i++) {
                    if (i > 0) rebuilt.append(",");
                    rebuilt.append(keyValuePairs.get(i));
                }
                rebuilt.append("}");
                
                String rebuiltJson = rebuilt.toString();
                if (isValidJson(rebuiltJson)) {
                    return rebuiltJson;
                }
            }
            
            // 如果仍然失败，返回空对象
            logger.warn("无法修复JSON，返回空对象: {}", json.substring(0, Math.min(100, json.length())));
            return "{}";
        }
        
        return json;
    }
    
    /**
     * 提取键值对
     */
    private static List<String> extractKeyValuePairs(String json) {
        List<String> pairs = new ArrayList<>();
        Pattern kvPattern = Pattern.compile("\"([^\"]+)\"\\s*:\\s*\"([^\"]+)\"");
        Matcher matcher = kvPattern.matcher(json);
        
        while (matcher.find()) {
            pairs.add("\"" + matcher.group(1) + "\":\"" + matcher.group(2) + "\"");
        }
        
        return pairs;
    }
    
    /**
     * 检查字符串是否为空
     */
    private static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 验证JSON是否有效
     */
    public static boolean isValidJson(String jsonString) {
        if (isNullOrEmpty(jsonString)) {
            return false;
        }
        
        try {
            OBJECT_MAPPER.readTree(jsonString);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 安全解析JSON
     */
    public static JsonNode safeParseJson(String jsonString) {
        try {
            String repairedJson = deepRepair(jsonString);
            return OBJECT_MAPPER.readTree(repairedJson);
        } catch (Exception e) {
            logger.error("JSON解析失败: {}", e.getMessage());
            try {
                return OBJECT_MAPPER.readTree("{}");
            } catch (JsonProcessingException ex) {
                return null;
            }
        }
    }
    
    /**
     * 格式化JSON字符串
     */
    public static String formatJson(String jsonString) {
        try {
            String repairedJson = deepRepair(jsonString);
            JsonNode jsonNode = OBJECT_MAPPER.readTree(repairedJson);
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(jsonNode);
        } catch (Exception e) {
            logger.error("JSON格式化失败: {}", e.getMessage());
            return jsonString;
        }
    }
    
    /**
     * 压缩JSON字符串（移除多余空白）
     */
    public static String compactJson(String jsonString) {
        try {
            String repairedJson = deepRepair(jsonString);
            JsonNode jsonNode = OBJECT_MAPPER.readTree(repairedJson);
            return OBJECT_MAPPER.writeValueAsString(jsonNode);
        } catch (Exception e) {
            logger.error("JSON压缩失败: {}", e.getMessage());
            return jsonString;
        }
    }
}