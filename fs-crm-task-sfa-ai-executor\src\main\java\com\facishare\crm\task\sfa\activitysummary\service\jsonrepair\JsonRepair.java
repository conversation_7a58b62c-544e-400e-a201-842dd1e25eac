package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair;

import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;

import java.io.IOException;
import java.io.Reader;
import java.util.List;

/**
 * Main entry point for JSON repair functionality.
 * 
 * This class provides methods to repair malformed JSON strings,
 * following the BNF definition and using heuristics to fix common issues:
 * - Add missing parentheses/brackets
 * - Quote strings or add missing quotes  
 * - Adjust whitespaces and remove line breaks
 */
public class JsonRepair {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Repair a JSON string and return the repaired JSON as a string.
     *
     * @param jsonStr The JSON string to repair
     * @return The repaired JSON string
     */
    public static String repairJson(String jsonStr) {
        return (String)repairJson(jsonStr, false, false, false);
    }
    
    /**
     * Repair a JSON string with advanced options.
     *
     * @param jsonStr The JSON string to repair
     * @param returnObjects If true, return the parsed object instead of JSON string
     * @param skipJsonLoads If true, skip initial JSON validation attempt
     * @param logging If true, enable logging of repair actions
     * @return The repaired JSON string or object
     */
    public static Object repairJson(String jsonStr, boolean returnObjects, boolean skipJsonLoads, boolean logging) {
        return repairJson(jsonStr, returnObjects, skipJsonLoads, logging, false);
    }
    
    /**
     * Repair a JSON string with full options.
     *
     * @param jsonStr The JSON string to repair
     * @param returnObjects If true, return the parsed object instead of JSON string
     * @param skipJsonLoads If true, skip initial JSON validation attempt
     * @param logging If true, enable logging of repair actions
     * @param streamStable If true, keep repair results stable for streaming JSON
     * @return The repaired JSON string or object
     */
    public static Object repairJson(String jsonStr, boolean returnObjects, boolean skipJsonLoads, 
                                   boolean logging, boolean streamStable) {
        if (jsonStr == null) {
            jsonStr = "";
        }
        
        JsonParser parser = new JsonParser(jsonStr, logging, streamStable);
        JsonParser.ParseResult result;
        
        if (skipJsonLoads) {
            result = parser.parse();
        } else {
            try {
                // Try to parse with standard JSON parser first
                Object validJson = objectMapper.readValue(jsonStr, Object.class);
                result = new JsonParser.ParseResult(validJson, null);
            } catch (JsonProcessingException e) {
                // If standard parsing fails, use our repair parser
                result = parser.parse();
            }
        }
        
        Object parsedJson = result.getResult();
        
        // Return objects directly if requested or if logging is enabled
        if (returnObjects || logging) {
            if (logging) {
                return new RepairResult(parsedJson, result.getLogs());
            } else {
                return parsedJson;
            }
        }
        
        // Return empty string for empty results
        if (parsedJson == null || parsedJson.equals("")) {
            return "";
        }
        
        // Convert back to JSON string
        try {
            return objectMapper.writeValueAsString(parsedJson);
        } catch (JsonProcessingException e) {
            return parsedJson.toString();
        }
    }
    
    /**
     * Parse JSON string and return the parsed object directly.
     * Drop-in replacement for standard JSON parsing libraries.
     *
     * @param jsonStr The JSON string to parse
     * @return The parsed object
     */
    public static Object loads(String jsonStr) {
        return repairJson(jsonStr, true, false, false);
    }
    
    /**
     * Parse JSON from a Reader and return the parsed object.
     *
     * @param reader The Reader containing JSON data
     * @return The parsed object
     * @throws IOException If reading fails
     */
    public static Object load(Reader reader) throws IOException {
        StringBuilder sb = new StringBuilder();
        char[] buffer = new char[1024];
        int length;
        while ((length = reader.read(buffer)) != -1) {
            sb.append(buffer, 0, length);
        }
        return loads(sb.toString());
    }
    
    /**
     * Parse JSON from a file path.
     *
     * @param filePath The path to the JSON file
     * @return The parsed object
     * @throws IOException If file reading fails
     */
    public static Object fromFile(String filePath) throws IOException {
        try (Reader reader = new java.io.FileReader(filePath)) {
            return load(reader);
        }
    }
    
    /**
     * Result wrapper for repair operations that include logging.
     */
    @Getter
    public static class RepairResult {
        private final Object result;
        private final List<JsonParser.LogEntry> logs;
        
        public RepairResult(Object result, List<JsonParser.LogEntry> logs) {
            this.result = result;
            this.logs = logs;
        }

    }
}