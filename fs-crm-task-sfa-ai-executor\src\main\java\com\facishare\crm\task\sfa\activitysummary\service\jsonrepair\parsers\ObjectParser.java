package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.parsers;

import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.Constants;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.ContextValues;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonContext;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonParser;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class ObjectParser implements JsonElementParser {
    
    @Override
    public Object parse(JsonParser parser) {
        Map<String, Object> obj = new LinkedHashMap<>();
        
        // Stop when you either find the closing brace or reach end of string
        while (true) {
            Character ch = parser.getCharAt();
            if (ch == null || ch == Constants.OBJECT_END) {
                break;
            }
            
            // Skip filler whitespaces
            parser.skipWhitespacesAt();
            
            // Sometimes LLMs do weird things, if we find a ":" so early, we'll change it to "," and move on
            ch = parser.getCharAt();
            if (ch != null && ch == Constants.COLON) {
                parser.log("While parsing an object we found a : before a key, ignoring");
                parser.setIndex(parser.getIndex() + 1);
            }
            
            // We are now searching for the string key
            parser.getContext().set(ContextValues.OBJECT_KEY);
            
            // Save this index in case we need to find a duplicate key
            int rollbackIndex = parser.getIndex();
            
            // <member> starts with a <string>
            String key = "";
            while (parser.getCharAt() != null) {
                rollbackIndex = parser.getIndex();
                
                // Check for array case
                if (parser.getCharAt() != null && parser.getCharAt() == Constants.ARRAY_START && key.isEmpty()) {
                    // Check if previous key's value is an array and merge if so
                    if (!obj.isEmpty()) {
                        String[] keys = obj.keySet().toArray(new String[0]);
                        String prevKey = keys[keys.length - 1];
                        Object prevValue = obj.get(prevKey);
                        
                        if (prevValue instanceof List) {
                            parser.setIndex(parser.getIndex() + 1);
                            Object newArray = new ArrayParser().parse(parser);
                            if (newArray instanceof List) {
                                @SuppressWarnings("unchecked")
                                List<Object> prevList = (List<Object>) prevValue;
                                @SuppressWarnings("unchecked")
                                List<Object> newList = (List<Object>) newArray;
                                prevList.addAll(newList);
                                
                                parser.skipWhitespacesAt();
                                if (parser.getCharAt() != null && parser.getCharAt() == Constants.COMMA) {
                                    parser.setIndex(parser.getIndex() + 1);
                                }
                                parser.skipWhitespacesAt();
                                continue;
                            }
                        }
                    }
                }
                
                Object keyObj = new StringParser().parse(parser);
                key = keyObj != null ? keyObj.toString() : "";
                
                if (key.isEmpty()) {
                    parser.skipWhitespacesAt();
                }
                
                ch = parser.getCharAt();
                if (!key.isEmpty() || (key.isEmpty() && (ch == null || ch == Constants.COLON || ch == Constants.OBJECT_END))) {
                    break;
                }
            }
            
            // Check for duplicate key in array context
            if (containsContextValue(parser.getContext(), ContextValues.ARRAY) && obj.containsKey(key)) {
                parser.log("While parsing an object we found a duplicate key, closing the object here and rolling back the index");
                parser.setIndex(rollbackIndex - 1);
                break;
            }
            
            // Skip filler whitespaces
            parser.skipWhitespacesAt();
            
            // Check if we reached the end
            ch = parser.getCharAt();
            if (ch == null || ch == Constants.OBJECT_END) {
                continue;
            }
            
            // Look for the ':' separator
            if (ch != Constants.COLON) {
                parser.log("While parsing an object, we expected a : after the key but found: " + ch);
                // Try to continue anyway
            } else {
                parser.setIndex(parser.getIndex() + 1);
            }
            
            // Skip filler whitespaces after ':'
            parser.skipWhitespacesAt();
            
            // Now we are looking for the value
            parser.getContext().reset(); // Remove OBJECT_KEY context
            parser.getContext().set(ContextValues.OBJECT_VALUE);
            
            // Parse the value
            Object value = parseValue(parser);
            
            // Store the key-value pair
            obj.put(key, value);
            
            // Reset context
            parser.getContext().reset(); // Remove OBJECT_VALUE context
            
            // Skip filler whitespaces
            parser.skipWhitespacesAt();
            
            // Check for comma or end of object
            ch = parser.getCharAt();
            if (ch == null || ch == Constants.OBJECT_END) {
                break;
            } else if (ch == Constants.COMMA) {
                parser.setIndex(parser.getIndex() + 1);
                parser.skipWhitespacesAt();
            }
        }
        
        // Skip the closing brace if present
        if (parser.getCharAt() != null && parser.getCharAt() == Constants.OBJECT_END) {
            parser.setIndex(parser.getIndex() + 1);
        }
        
        return obj;
    }
    
    private Object parseValue(JsonParser parser) {
        Character ch = parser.getCharAt();
        if (ch == null) {
            return "";
        }
        
        char c = ch;
        
        // <object> starts with '{'
        if (c == Constants.OBJECT_START) {
            parser.setIndex(parser.getIndex() + 1);
            return new ObjectParser().parse(parser);
        }
        // <array> starts with '['
        else if (c == Constants.ARRAY_START) {
            parser.setIndex(parser.getIndex() + 1);
            return new ArrayParser().parse(parser);
        }
        // <string> starts with a quote or letter
        else if (Constants.STRING_DELIMITERS.contains(c) || Character.isLetter(c)) {
            return new StringParser().parse(parser);
        }
        // <number> starts with [0-9] or minus
        else if (Character.isDigit(c) || c == '-' || c == '.') {
            return new NumberParser().parse(parser);
        }
        else if (c == Constants.COMMENT_HASH || c == Constants.COMMENT_SLASH) {
            return new CommentParser().parse(parser);
        }
        else {
            // Skip unknown character and return empty string
            parser.setIndex(parser.getIndex() + 1);
            return "";
        }
    }
    
    private boolean containsContextValue(JsonContext context, ContextValues value) {
        // Simple implementation - could be enhanced to check full context stack
        return context.getCurrent() == value;
    }
}