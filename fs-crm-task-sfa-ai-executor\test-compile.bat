@echo off
echo Compiling JsonRepairTest...

rem Set classpath for compilation
set CLASSPATH=target\classes;target\test-classes;%USERPROFILE%\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;%USERPROFILE%\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;%USERPROFILE%\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;%USERPROFILE%\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;%USERPROFILE%\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar

rem Compile the test class
javac -cp "%CLASSPATH%" -encoding UTF-8 src\test\java\com\facishare\crm\task\sfa\activitysummary\service\jsonrepair\JsonRepairTest.java

if %ERRORLEVEL% EQU 0 (
    echo Compilation successful!
) else (
    echo Compilation failed!
)

pause
