package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * JSON修复服务测试
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class JsonRepairServiceTest {

    @Autowired
    private JsonRepairService jsonRepairService;

    @Autowired
    private AdvancedJsonRepairService advancedJsonRepairService;

    @Test
    public void testRepairUnquotedKeys() {
        String malformedJson = "{name: \"<PERSON>\", age: 30}";
        String repairedJson = jsonRepairService.repairJson(malformedJson);
        
        assertEquals("{\"name\": \"John\", \"age\": 30}", repairedJson);
        assertTrue(jsonRepairService.isValidJson(repairedJson));
    }

    @Test
    public void testRepairSingleQuotes() {
        String malformedJson = "{'name': 'John', 'city': 'New York'}";
        String repairedJson = jsonRepairService.repairJson(malformedJson);
        
        assertTrue(jsonRepairService.isValidJson(repairedJson));
        JsonNode jsonNode = jsonRepairService.safeParseJson(repairedJson);
        
        assertNotNull(jsonNode);
        assertEquals("John", jsonNode.get("name").asText());
        assertEquals("New York", jsonNode.get("city").asText());
    }

    @Test
    public void testRemoveTrailingCommas() {
        String malformedJson = "{\"name\": \"John\", \"age\": 30,}";
        String repairedJson = jsonRepairService.repairJson(malformedJson);
        
        assertTrue(jsonRepairService.isValidJson(repairedJson));
        assertFalse(repairedJson.contains(",}"));
    }

    @Test
    public void testUtilQuickRepair() {
        String malformedJson = "{name: 'John', age: 30,}";
        String repairedJson = JsonRepairUtil.quickRepair(malformedJson);
        
        assertTrue(JsonRepairUtil.isValidJson(repairedJson));
    }

    @Test
    public void testUtilDeepRepair() {
        String malformedJson = "{\"name\": \"John\" \"age\": 30";
        String repairedJson = JsonRepairUtil.deepRepair(malformedJson);
        
        assertTrue(JsonRepairUtil.isValidJson(repairedJson));
        
        JsonNode jsonNode = JsonRepairUtil.safeParseJson(repairedJson);
        assertNotNull(jsonNode);
        assertEquals("John", jsonNode.get("name").asText());
        assertEquals(30, jsonNode.get("age").asInt());
    }

    @Test
    public void testAdvancedRepairWithStrategy() {
        String malformedJson = "{name: 'John', details: {city: 'NYC', country: 'US'";
        
        AdvancedJsonRepairService.RepairResult result = 
            advancedJsonRepairService.repairJson(malformedJson, AdvancedJsonRepairService.RepairStrategy.DEEP);
        
        assertTrue(result.isSuccess());
        assertTrue(jsonRepairService.isValidJson(result.getRepairedJson()));
        assertFalse(result.getRepairActions().isEmpty());
        
        System.out.println("修复操作: " + result.getRepairActions());
        System.out.println("修复结果: " + result.getRepairedJson());
    }

    @Test
    public void testAdvancedRepairWithConfig() {
        String malformedJson = "{name: 'John', age: 30,}";
        
        JsonRepairConfig config = JsonRepairConfig.STRICT;
        AdvancedJsonRepairService.RepairResult result = 
            advancedJsonRepairService.repairJson(malformedJson, 
                AdvancedJsonRepairService.RepairStrategy.QUICK, config);
        
        assertTrue(result.isSuccess());
        assertTrue(jsonRepairService.isValidJson(result.getRepairedJson()));
    }

    @Test
    public void testAggressiveRepair() {
        String malformedJson = "{\"name\": \"John\", \"details\": {\"city\": \"New";
        
        AdvancedJsonRepairService.RepairResult result = 
            advancedJsonRepairService.repairJson(malformedJson, 
                AdvancedJsonRepairService.RepairStrategy.AGGRESSIVE);
        
        assertTrue(result.isSuccess());
        assertTrue(jsonRepairService.isValidJson(result.getRepairedJson()));
        
        JsonNode jsonNode = jsonRepairService.safeParseJson(result.getRepairedJson());
        assertNotNull(jsonNode);
        assertEquals("John", jsonNode.get("name").asText());
    }

    @Test
    public void testEmptyAndNullInput() {
        assertEquals("{}", jsonRepairService.repairJson(null));
        assertEquals("{}", jsonRepairService.repairJson(""));
        assertEquals("{}", jsonRepairService.repairJson("   "));
    }

    @Test
    public void testAlreadyValidJson() {
        String validJson = "{\"name\": \"John\", \"age\": 30}";
        String result = jsonRepairService.repairJson(validJson);
        
        assertEquals(validJson.trim(), result);
        assertTrue(jsonRepairService.isValidJson(result));
    }

    @Test
    public void testComplexRepair() {
        String malformedJson = "{\n" +
                "  name: 'John Doe',\n" +
                "  age: 30,\n" +
                "  // This is a comment\n" +
                "  city: \"New York\",\n" +
                "  active: TRUE,\n" +
                "  details: {\n" +
                "    email: '<EMAIL>'\n" +
                "    phone: '************',\n" +
                "  },\n" +
                "}";

        String repairedJson = JsonRepairUtil.deepRepair(malformedJson);
        assertTrue(JsonRepairUtil.isValidJson(repairedJson));
        
        JsonNode jsonNode = JsonRepairUtil.safeParseJson(repairedJson);
        assertNotNull(jsonNode);
        assertEquals("John Doe", jsonNode.get("name").asText());
        assertEquals(30, jsonNode.get("age").asInt());
        assertEquals("New York", jsonNode.get("city").asText());
        assertTrue(jsonNode.get("active").asBoolean());
        
        JsonNode details = jsonNode.get("details");
        assertNotNull(details);
        assertEquals("<EMAIL>", details.get("email").asText());
        assertEquals("************", details.get("phone").asText());
    }

    @Test
    public void testFormatAndCompactJson() {
        String malformedJson = "{name:'John',age:30}";
        
        String formatted = JsonRepairUtil.formatJson(malformedJson);
        assertTrue(formatted.contains("\n")); // 格式化后应该有换行
        
        String compact = JsonRepairUtil.compactJson(malformedJson);
        assertFalse(compact.contains("\n")); // 压缩后不应该有换行
        
        assertTrue(JsonRepairUtil.isValidJson(formatted));
        assertTrue(JsonRepairUtil.isValidJson(compact));
    }
}