package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core;


import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.parsers.*;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
@Data
public class JsonParser {
    private final String jsonStr;
    private int index;
    private final JsonContext context;
    private final boolean logging;
    private final List<LogEntry> logger;
    private final boolean streamStable;

    // Parser delegates
    private final ArrayParser arrayParser;
    private final ObjectParser objectParser;
    private final StringParser stringParser;
    private final NumberParser numberParser;
    private final BooleanOrNullParser booleanOrNullParser;
    private final CommentParser commentParser;

    public JsonParser(String jsonStr, boolean logging, boolean streamStable) {
        this.jsonStr = jsonStr != null ? jsonStr : "";
        this.index = 0;
        this.context = new JsonContext();
        this.logging = logging;
        this.logger = logging ? new ArrayList<>() : null;
        this.streamStable = streamStable;

        // Initialize parsers
        this.arrayParser = new ArrayParser();
        this.objectParser = new ObjectParser();
        this.stringParser = new StringParser();
        this.numberParser = new NumberParser();
        this.booleanOrNullParser = new BooleanOrNullParser();
        this.commentParser = new CommentParser();
    }

    public ParseResult parse() {
        Object json = parseJson();
        
        if (index < jsonStr.length()) {
            log("The parser returned early, checking if there's more json elements");
            List<Object> jsonList = new ArrayList<>();
            jsonList.add(json);
            
            while (index < jsonStr.length()) {
                Object j = parseJson();
                if (j != null && !j.equals("")) {
                    // Simple object comparison - in a real implementation, 
                    // you might want more sophisticated comparison
                    if (isSameObject(jsonList.get(jsonList.size() - 1), j)) {
                        jsonList.remove(jsonList.size() - 1);
                    }
                    jsonList.add(j);
                } else {
                    index++;
                }
            }
            
            if (jsonList.size() == 1) {
                log("There were no more elements, returning the element without the array");
                json = jsonList.get(0);
            } else {
                json = jsonList;
            }
        }
        
        return new ParseResult(json, logging ? new ArrayList<>(logger) : null);
    }

    private Object parseJson() {
        while (true) {
            Character ch = getCharAt();
            if (ch == null) {
                return "";
            }
            
            char c = ch;
            
            // <object> starts with '{'
            if (c == Constants.OBJECT_START) {
                index++;
                return objectParser.parse(this);
            }
            // <array> starts with '['
            else if (c == Constants.ARRAY_START) {
                index++;
                return arrayParser.parse(this);
            }
            // <string> starts with a quote
            else if (!context.isEmpty() && (Constants.STRING_DELIMITERS.contains(c) || Character.isLetter(c))) {
                return stringParser.parse(this);
            }
            // <number> starts with [0-9] or minus
            else if (!context.isEmpty() && (Character.isDigit(c) || c == '-' || c == '.')) {
                return numberParser.parse(this);
            }
            else if (c == Constants.COMMENT_HASH || c == Constants.COMMENT_SLASH) {
                return commentParser.parse(this);
            }
            // If everything else fails, we just ignore and move on
            else {
                index++;
            }
        }
    }

    public Character getCharAt() {
        return getCharAt(0);
    }

    public Character getCharAt(int offset) {
        try {
            return jsonStr.charAt(index + offset);
        } catch (IndexOutOfBoundsException e) {
            return null;
        }
    }

    public void skipWhitespacesAt() {
        skipWhitespacesAt(0, true);
    }

    public int skipWhitespacesAt(int idx, boolean moveMainIndex) {
        try {
            char ch = jsonStr.charAt(index + idx);
            while (Character.isWhitespace(ch)) {
                if (moveMainIndex) {
                    index++;
                } else {
                    idx++;
                }
                ch = jsonStr.charAt(index + idx);
            }
        } catch (IndexOutOfBoundsException e) {
            // End of string reached
        }
        return idx;
    }

    public int skipToCharacter(char character, int idx) {
        try {
            char ch = jsonStr.charAt(index + idx);
            while (ch != character) {
                idx++;
                ch = jsonStr.charAt(index + idx);
            }
        } catch (IndexOutOfBoundsException e) {
            // End of string reached
        }
        return idx;
    }

    public void log(String text) {
        if (logging) {
            int window = 10;
            int start = Math.max(index - window, 0);
            int end = Math.min(index + window, jsonStr.length());
            String contextStr = jsonStr.substring(start, end);
            logger.add(new LogEntry(text, contextStr));
        }
    }

    private boolean isSameObject(Object obj1, Object obj2) {
        // Simple comparison - could be enhanced for deep object comparison
        return obj1 != null && obj1.equals(obj2);
    }


    @Getter
    public static class ParseResult {
        private final Object result;
        private final List<LogEntry> logs;

        public ParseResult(Object result, List<LogEntry> logs) {
            this.result = result;
            this.logs = logs;
        }

    }

    @Getter
    public static class LogEntry {
        private final String text;
        private final String context;

        public LogEntry(String text, String context) {
            this.text = text;
            this.context = context;
        }

    }
}