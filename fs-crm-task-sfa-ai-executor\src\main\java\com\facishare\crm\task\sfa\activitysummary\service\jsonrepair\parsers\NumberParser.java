package com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.parsers;


import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.Constants;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.ContextValues;
import com.facishare.crm.task.sfa.activitysummary.service.jsonrepair.core.JsonParser;

import java.util.HashSet;
import java.util.Set;

public class NumberParser implements JsonElementParser {
    private static final Set<Character> NUMBER_CHARS = new HashSet<>();
    
    static {
        String chars = "0123456789-.eE/,";
        for (char c : chars.toCharArray()) {
            NUMBER_CHARS.add(c);
        }
    }
    
    @Override
    public Object parse(JsonParser parser) {
        StringBuilder numberStr = new StringBuilder();
        Character ch = parser.getCharAt();
        boolean isArray = parser.getContext().getCurrent() == ContextValues.ARRAY;
        
        while (ch != null && NUMBER_CHARS.contains(ch) && (!isArray || ch != ',')) {
            numberStr.append(ch);
            parser.setIndex(parser.getIndex() + 1);
            ch = parser.getCharAt();
        }
        
        // Handle invalid ending characters
        if (numberStr.length() > 0) {
            char lastChar = numberStr.charAt(numberStr.length() - 1);
            if (lastChar == '-' || lastChar == 'e' || lastChar == 'E' || lastChar == '/' || lastChar == ',') {
                numberStr.deleteCharAt(numberStr.length() - 1);
                parser.setIndex(parser.getIndex() - 1);
            }
        }
        
        // Check if this is actually a string (followed by alphabetic characters)
        Character nextChar = parser.getCharAt();
        if (nextChar != null && Character.isLetter(nextChar)) {
            // This was a string instead, revert and parse as string
            parser.setIndex(parser.getIndex() - numberStr.length());
            return new StringParser().parse(parser);
        }
        
        String numStr = numberStr.toString();
        if (numStr.isEmpty()) {
            return Constants.EMPTY_STRING;
        }
        
        try {
            // Handle comma-separated numbers as strings (currency format)
            if (numStr.contains(",")) {
                return numStr;
            }
            
            // Parse as float if contains decimal point or scientific notation
            if (numStr.contains(".") || numStr.toLowerCase().contains("e")) {
                return Double.parseDouble(numStr);
            } else {
                // Parse as integer
                return Long.parseLong(numStr);
            }
        } catch (NumberFormatException e) {
            return numStr; // Return as string if parsing fails
        }
    }
}